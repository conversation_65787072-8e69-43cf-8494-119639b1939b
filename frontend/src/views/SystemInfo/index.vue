<template>
  <div class="systembox">
    <div class="systembox-top">
      <!-- 本地磁盘空间 -->
      <div class="systembox-top-l">
        <div class="title">
          <svg-icon icon-class="diskderive" style="margin-right: 8px" />
          本地磁盘空间
        </div>
        <div v-if="show1" class="waringtext">
          本地磁盘空间占用已高于90%，请尽快处理!
        </div>
        <article>
          <div class="systembox-top-l-box1">
            <v-chart
              ref="chart"
              :option="taskline_options"
              autoresize
              :update-options="{ notMerge: true }"
            >
            </v-chart>
            <div class="ifon">
              <span>0%</span>
              <span>使用占比</span>
              <span>100%</span>
            </div>
          </div>
        </article>
        <div class="systembox-top-l-box2">
          <div>
            <span class="fontstyle" style="margin-bottom: 4px">总量</span>
            <span v-if="wsInfo.totalLocalDiskSpace" style="font-size: 24px">{{
              $unitValue(wsInfo.totalLocalDiskSpace, 2)
            }}</span>
          </div>
          <div>
            <span class="fontstyle" style="margin-bottom: 4px">剩余余量</span>
            <span v-if="wsInfo.emptyLocalDiskSpace" style="font-size: 24px">{{
              $unitValue(wsInfo.emptyLocalDiskSpace, 2)
            }}</span>
          </div>
          <div>
            <span class="fontstyle" style="margin-bottom: 4px">inode已使用</span>
            <span v-if="wsInfo.emptyLocalDiskSpace" style="font-size: 24px">{{ $formatToFixed(wsInfo.localDiskSpaceINode,2) }}%</span>
          </div>
          <div :class="parseInt(sysinfo.io_state) > 90 ? 'active' : 'noactive'">
            <span class="fontstyle" style="margin-bottom: 4px; color: #ffffff">I/O使用率</span>
            <span>{{
              $formatToFixed(wsInfo.localDiskSpaceIOUtilizationRate)
            }}%</span>
          </div>
        </div>
      </div>
      <!-- CPU -->
      <div class="systembox-top-m">
        <div class="title">
          <svg-icon icon-class="cpu" style="margin-right: 8px" />
          CPU
        </div>
        <div
          v-if="$formatToFixed(wsInfo.cpuUsageRate) >= 90"
          class="waringtext"
        >
          CPU占用已高于90%!
        </div>
        <div class="systembox-top-m-gress1">
          <el-progress
            type="circle"
            :percentage="$formatToFixed(wsInfo.cpuUsageRate, 1)"
            :stroke-width="num"
          ></el-progress>
          <div class="text">使用占比</div>
        </div>
        <div class="systembox-top-m-box">
          <div class="fontstyle" style="margin-bottom: 4px">处理器</div>
          <div>{{ wsInfo.processor }}</div>
        </div>
        <div class="systembox-top-m-box1">
          <div>
            <span class="fontstyle" style="margin-bottom: 4px">核心</span>
            <span>{{ wsInfo.numberOfProcessorCores }}核</span>
          </div>
          <div class="line"></div>
          <div>
            <span class="fontstyle" style="margin-bottom: 4px">CPU负载平均值</span>
            <span>{{ $formatToFixed(wsInfo.averageCpuLoad, 1) }}%</span>
          </div>
        </div>
      </div>
      <!-- 内存 -->
      <div class="systembox-top-r">
        <div class="title">
          <svg-icon icon-class="memory" style="margin-right: 8px" />
          内存
          <div
            v-if="$formatToFixed(wsInfo.memoryUsageRate) >= 90"
            class="waringtext"
          >
            物理内存占用已高于90%!
          </div>
        </div>
        <div class="systembox-top-r-gress">
          <el-progress
            type="circle"
            :percentage="$formatToFixed(wsInfo.memoryUsageRate, 1)"
            :stroke-width="num"
          ></el-progress>
          <div class="text">使用占比</div>
        </div>
        <div class="systembox-top-r-box1">
          <div>
            <span class="fontstyle" style="margin-bottom: 4px">总量</span>
            <span>{{ $unitValue(wsInfo.totalMemory, 1) }}</span>
          </div>
          <div class="line"></div>
          <div>
            <span class="fontstyle" style="margin-bottom: 4px">余量</span>
            <span>{{ $unitValue(wsInfo.emptyMemory, 1) }}</span>
          </div>
        </div>
        <div class="systembox-top-r-box1" style="margin-top: 8px">
          <div>
            <span class="fontstyle" style="margin-bottom: 4px">CACHE</span>
            <span>{{ $unitValue(wsInfo.cache, 1) }}</span>
          </div>
          <div class="line"></div>
          <div>
            <span class="fontstyle" style="margin-bottom: 4px">BUFFER</span>
            <span>{{ $unitValue(wsInfo.buffer, 1) }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="systembox-down">
      <div class="systembox-down-l">
        <!-- 系统相关 -->
        <div class="top">
          <div class="title">
            <svg-icon icon-class="systemAbout" style="margin-right: 8px" />
            系统相关
          </div>
          <div>
            <span class="headline">系统主机名</span>
            <span class="headline-l">{{ sysinfo.hostname }}</span>
          </div>
          <div>
            <span class="headline">操作系统</span>
            <span class="headline-l">{{ sysinfo.osinfo }}</span>
          </div>
          <div>
            <span class="headline">系统时间</span>
            <span class="headline-l">{{ sysinfo.timeS }}</span>
          </div>
          <div>
            <span class="headline">系统启动时间</span>
            <span class="headline-l">{{ $formatDateOne(sysinfo.startTime) }}</span>
          </div>
          <div>
            <span class="headline">系统运行时长</span>
            <span class="headline-l">{{ $formatHH(sysinfo.time) }}</span>
          </div>
        </div>
        <!-- 产品相关 -->
        <div class="down">
          <div class="title">
            <svg-icon icon-class="product-icon" style="margin-right: 8px" />
            产品相关
          </div>
          <div>
            <span class="headline">产品型号</span>
            <span class="headline-l">{{ product.product }}</span>
          </div>
          <div>
            <span class="headline">产品版本号</span>
            <span class="headline-l">{{ product.version }}</span>
          </div>
          <div>
            <span class="headline">产品SN码</span>
            <span class="headline-l">{{ product.sN }}</span>
          </div>
          <div>
            <span class="headline">产品到期时间</span>
            <span class="headline-l">{{ $processingTime(product.privilegedTime) }}</span>
          </div>
        </div>
      </div>
      <!-- 当前进程信息 -->
      <div class="systembox-down-r">
        <div class="title">
          <svg-icon icon-class="course-icon" style="margin-right: 8px" />
          组件状态
        </div>
        <div class="systembox-down-r-tab">
          <el-tabs v-model="tabPosition" type="card">
            <el-tab-pane
              v-for="(item, index) in wsInfo.currentProcessInformationList"
              :key="index"
              :label="item.name"
              :name="item.name"
            >
              <div class="box">
                <div>
                  <span class="headline">开始时间</span>
                  <span class="headline-l">{{
                    $processingTime(item.processStartTime)
                  }}</span>
                </div>
                <div>
                  <span class="headline">CPU占用时间</span>
                  <span class="headline-l">{{
                    $formatHH(item.processCpuUsageTime)
                  }}</span>
                </div>
                <div>
                  <span class="headline">内存占用</span>
                  <span class="headline-l">{{
                    $unitValue(item.memoryUsage)
                  }}</span>
                </div>
                <div>
                  <span class="headline">名称</span>
                  <span class="headline-l">{{ item.processName }}</span>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
    <svg v-if="percentagesvg1" width="1" height="1">
      <defs>
        <linearGradient id="write" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop
            offset="0%"
            style="stop-color: #116ef9"
            stop-opacity="0.5"
          ></stop>
          <stop
            offset="100%"
            style="stop-color: #116ef9"
            stop-opacity="1"
          ></stop>
        </linearGradient>
      </defs>
    </svg>
    <svg v-if="percentagesvg2" width="1" height="1">
      <defs>
        <linearGradient id="write" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop
            offset="0%"
            style="stop-color: #fa8213"
            stop-opacity="0.5"
          ></stop>
          <stop
            offset="100%"
            style="stop-color: #fa8213"
            stop-opacity="1"
          ></stop>
        </linearGradient>
      </defs>
    </svg>
    <svg v-if="percentagesvg3" width="1" height="1">
      <defs>
        <linearGradient id="write" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop
            offset="0%"
            style="stop-color: #f91111"
            stop-opacity="0.5"
          ></stop>
          <stop
            offset="100%"
            style="stop-color: #f91111"
            stop-opacity="1"
          ></stop>
        </linearGradient>
      </defs>
    </svg>
    <svg v-if="percentagesvg4" width="1" height="1">
      <defs>
        <linearGradient id="write2" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop
            offset="0%"
            style="stop-color: #116ef9"
            stop-opacity="0.5"
          ></stop>
          <stop
            offset="100%"
            style="stop-color: #116ef9"
            stop-opacity="1"
          ></stop>
        </linearGradient>
      </defs>
    </svg>
    <svg v-if="percentagesvg5" width="1" height="1">
      <defs>
        <linearGradient id="write2" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop
            offset="0%"
            style="stop-color: #fa8213"
            stop-opacity="0.5"
          ></stop>
          <stop
            offset="100%"
            style="stop-color: #fa8213"
            stop-opacity="1"
          ></stop>
        </linearGradient>
      </defs>
    </svg>
    <svg v-if="percentagesvg6" width="1" height="1">
      <defs>
        <linearGradient id="write2" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop
            offset="0%"
            style="stop-color: #f91111"
            stop-opacity="0.5"
          ></stop>
          <stop
            offset="100%"
            style="stop-color: #f91111"
            stop-opacity="1"
          ></stop>
        </linearGradient>
      </defs>
    </svg>
  </div>
</template>

<script>
import { taskline_options, category_options } from "./systeminfoline";
import { getsysteminfo } from "@/api/system";
export default {
  data() {
    return {
      taskline_options,
      category_options,
      tabPosition: "探针主任务",
      product: {},
      system: {},
      sysinfo: {},
      num: 12,
      percentage: 90,
      percentage2: 65,
      percentagesvg1: false,
      percentagesvg2: false,
      percentagesvg3: false,
      percentagesvg4: false,
      percentagesvg5: false,
      percentagesvg6: false,
      show1: false,
      wsInfo: {},
    };
  },
  created() {
    this.getsysteminfo();
    this.WS_INIT();
  },
  beforeDestroy(){
    if(this.ws){
      this.ws.close();
      this.ws = null;
    }
  },
  methods: {
    // 获取系统和产品相头的信息
    async getsysteminfo() {
      try {
        const {data} = await getsysteminfo();
        this.sysinfo=JSON.parse(data.system);
        this.product=JSON.parse(data.product);
        
      } catch (error) {
        console.log(error);
      }
    },
    formatColor() {
      // cpu进度圈的颜色判断
      if (1 < this.percentage && this.percentage <= 59) {
        this.percentagesvg1 = true;
        this.percentagesvg2 = false;
        this.percentagesvg3 = false;
      } else if (60 <= this.percentage && this.percentage <= 89) {
        this.percentagesvg1 = false;
        this.percentagesvg2 = true;
        this.percentagesvg3 = false;
      } else if (90 <= this.percentage && this.percentage <= 100) {
        this.percentagesvg1 = false;
        this.percentagesvg2 = false;
        this.percentagesvg3 = true;
      }
      // 内存进度圈的颜色判断
      if (1 < this.percentage2 && this.percentage2 <= 59) {
        this.percentagesvg4 = true;
        this.percentagesvg5 = false;
        this.percentagesvg6 = false;
      } else if (60 <= this.percentage2 && this.percentage2 <= 89) {
        this.percentagesvg4 = false;
        this.percentagesvg5 = true;
        this.percentagesvg6 = false;
      } else if (90 <= this.percentage2 && this.percentage2 <= 100) {
        this.percentagesvg4 = false;
        this.percentagesvg5 = false;
        this.percentagesvg6 = true;
      }
    },
    // ws初始化
    WS_INIT() {
      let baseURL;
      if (process.env.VUE_APP_BASE_API === '') {
        const wscol = window.location.protocol === 'http:' ? 'ws' : 'wss';
        baseURL = `${wscol}://${window.location.host}/wss_api/monitor/websocket/1`;
      } else {
        baseURL = `${process.env.VUE_APP_BASE_WSS2}/monitor/websocket/1`;
      }
      if ("WebSocket" in window) {
        this.ws = new WebSocket(baseURL);
        this.ws.onopen = this.WS_OPEN;
        this.ws.onmessage = this.WS_MESSAGE;
        this.ws.onclose = this.WS_CLOSE;
        this.ws.onerror = this.WS_ERROR;
      }
    },
    // ws初次建立连接
    WS_OPEN(e) {
      this.ws.send("planet");
      console.warn("当前连接状态：", this.ws.readyState);
    },
    // ws接收消息
    WS_MESSAGE(e) {
      const data = JSON.parse(e.data);
      this.percentage = this.$formatToFixed(data.cpuUsageRate, 0);
      this.percentage2 = this.$formatToFixed(data.memoryUsageRate, 0);
      this.taskline_options.series[0].data[0].value = this.$formatToFixed(
        data.localDiskSpaceUsageProportion
      );
      this.show1 = this.taskline_options.series[0].data[0].value >= 90;
      this.wsInfo = data;
      // this.tabPosition = this.wsInfo.currentProcessInformationList[0].name;
      this.formatColor();
    },
    // WS关闭
    WS_CLOSE(e) {
      console.warn("当前连接状态：", this.ws.readyState);
      this.$message.error("当前连接状态关闭");
    },
    // ws出错
    WS_ERROR(err) {
      console.error("当前连接状态：", this.ws.readyState);
      this.$message.error("当前连接状态出错");
    },
    showProgressColor: function (val) {
      if (1 < val && val <= 59) {
        return (val = "el-bg-inner-running");
      } else if (60 <= val && val <= 89) {
        return (val = "el-bg-inner-error");
      } else if (90 <= val && val <= 100) {
        return (val = "el-bg-inner-done");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.systembox {
  .headtitle {
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    color: #000000;
    margin-bottom: 14px;
  }

  .title {
    font-family: "Alibaba PuHuiTi";
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    color: #2c2c35;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .whitebox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    position: absolute;
    top: 4.2px;

    div {
      width: 1.5px;
      height: 8px;
      background: #f7f8fa;
      border-radius: 1px;
    }
  }

  .fontstyle {
    font-family: "Alibaba PuHuiTi";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #767684;
  }

  &-top {
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    color: #000000;
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;

    &-l {
      position: relative;
      padding: 16px;
      min-width: 856px;
      height: 448px;
      background: #ffffff;
      // box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
      border-radius: 8px;
      margin-right: 16px;

      .waringtext {
        border-radius: 8px;
        padding: 0 5px;
        background: #fce7e7;
        position: absolute;
        top: 30px;
        left: 50%;
        transform: translate(-50%, 0);
        color: #f56c6c;
        font-size: 10px;
      }

      &-box1 {
        width: 100%;
        height: 380px;
        position: relative;
        .ifon {
          font-weight: 400;
          font-size: 20px;
          line-height: 28px;
          color: #9999a1;
          width: 40%;
          position: absolute;
          bottom: 75px;
          left: 255px;
          display: flex;
          justify-content: space-between;
        }
      }

      &-box2 {
        padding: 0 8px;
        position: absolute;
        bottom: 15px;
        left: 0px;
        width: 100%;
        display: flex;
        justify-content: space-between;

        .noactive {
          background: linear-gradient(
            122.58deg,
            #116ef9 30.51%,
            rgba(17, 110, 249, 0.7) 100%
          );
          font-weight: 500;
          font-size: 24px;
          line-height: 32px;
          color: #ffffff;
          border: 1px solid #4a97ff;
          border-radius: 8px;
        }

        .active {
          background: linear-gradient(
            122.58deg,
            #f91111 30.51%,
            rgba(249, 17, 17, 0.7) 100%
          );
          font-weight: 500;
          font-size: 24px;
          line-height: 32px;
          color: #ffffff;
          border: 1px solid #f91111;
          border-radius: 8px;
        }

        div {
          padding: 12px;
          width: 200px;
          height: 74px;
          background: #f7f8fa;
          border-radius: 8px;
          display: flex;
          flex-direction: column;
          justify-content: center;
        }

        .newly {
          padding: 0 !important;
          width: 100%;
        }
      }
    }

    &-m {
      position: relative;
      padding: 16px;
      margin-right: 16px;
      // width: 422px;
      width: 100%;
      height: 448px;
      background: #ffffff;
      // box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
      border-radius: 8px;
      font-family: "Alibaba PuHuiTi";
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      color: #2c2c35;

      .waringtext {
        border-radius: 8px;
        padding: 0 5px;
        background: #fce7e7;
        position: absolute;
        top: 30px;
        left: 50%;
        transform: translate(-50%, 0);
        color: #f56c6c;
        font-size: 10px;
      }

      &-box {
        padding: 12px;
        width: 100%;
        height: 74px;
        background: #f7f8fa;
        border-top: 1px solid #f2f3f7;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      &-box1 {
        margin-top: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        div {
          padding: 12px;
          // width: 128px;
          width: 100%;
          height: 74px;
          background: #f7f8fa;
          border-radius: 8px;
          display: flex;
          flex-direction: column;
          justify-content: center;
        }

        .line {
          width: 8px;
          height: 74px;
          background: #fff;
        }
      }

      &-gress1 {
        position: relative;
        margin: 30px 0;
        display: flex;
        justify-content: center;

        ::v-deep {
          .el-progress-circle {
            width: 170px !important;
            height: 170px !important;
          }

          svg > path:nth-child(2) {
            stroke: url(#write);
          }

          .el-progress__text {
            color: #000000;
            font-weight: bold;
            font-size: 24px !important;
          }
        }

        .text {
          position: absolute;
          bottom: 46px;
          font-style: normal;
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
          color: #9999a1;
        }
      }
    }

    &-r {
      position: relative;
      padding: 16px;
      // width: 422px;
      width: 100%;
      height: 448px;
      background: #ffffff;
      // box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
      border-radius: 8px;
      font-family: "Alibaba PuHuiTi";
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      color: #2c2c35;

      .waringtext {
        border-radius: 8px;
        padding: 0 5px;
        background: #fce7e7;
        position: absolute;
        top: 30px;
        left: 50%;
        transform: translate(-50%, 0);
        color: #f56c6c;
        font-size: 10px;
      }

      &-box {
        margin-top: 8px;
        padding: 12px;
        // width: 264px;
        width: 100%;
        height: 74px;
        background: #f7f8fa;
        border-top: 1px solid #f2f3f7;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        justify-content: center;

        ::v-deep {
          .el-progress-bar__outer {
            width: 100%;
            height: 8px !important;
          }

          .el-progress-bar {
            padding: 0 !important;
          }

          .el-icon-warning:before {
            content: none;
          }

          .el-bg-inner-running .el-progress-bar__inner {
            border-radius: 0 !important;
            background-color: unset;
            background: linear-gradient(
              122.58deg,
              #116ef9 30.51%,
              rgba(17, 110, 249, 0.7) 100%
            );
          }

          .el-bg-inner-error .el-progress-bar__inner {
            border-radius: 0 !important;
            background-color: unset;
            background: linear-gradient(
              122.58deg,
              #fa8213 30.51%,
              rgba(250, 130, 19, 0.7) 100%
            );
          }

          .el-bg-inner-done .el-progress-bar__inner {
            border-radius: 0 !important;
            background-color: unset;
            background: linear-gradient(
              122.58deg,
              #f91111 30.51%,
              rgba(249, 17, 17, 0.7) 100%
            );
          }

          .el-progress-bar__outer {
            border-radius: 0 !important;
          }
        }
      }

      &-box1 {
        display: flex;
        justify-content: space-between;
        align-items: center;

        div {
          padding: 12px;
          // width: 128px;
          width: 100%;
          height: 74px;
          background: #f7f8fa;
          border-radius: 8px;
          display: flex;
          flex-direction: column;
          justify-content: center;
        }

        .line {
          width: 8px;
          height: 74px;
          background: #ffffff;
        }
      }

      &-gress {
        position: relative;
        margin: 30px 0;
        display: flex;
        justify-content: center;

        ::v-deep {
          .el-progress-circle {
            width: 170px !important;
            height: 170px !important;
          }

          svg > path:nth-child(2) {
            stroke: url(#write2);
          }

          .el-progress__text {
            color: #000000;
            font-weight: bold;
            font-size: 24px !important;
          }
        }

        .text {
          position: absolute;
          bottom: 46px;
          font-style: normal;
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
          color: #9999a1;
        }
      }
    }
  }

  &-down {
    display: flex;
    justify-content: space-between;

    &-l {
      // padding: 16px;
      margin-right: 16px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      font-family: "Alibaba PuHuiTi";
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      color: #2c2c35;
      min-width: 856px;

      .top {
        padding: 16px;
        // width: 856px;
        width: 100%;
        height: 194px;
        background: #ffffff;
        // box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        div {
          display: flex;
          justify-content: flex-start;

          .headline {
            color: #767684;
            width: 150px;
          }

          .headline-l {
            flex: 1;
          }
        }
      }

      .down {
        padding: 16px;
        // width: 856px;
        width: 100%;
        height: 216px;
        background: #ffffff;
        // box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .title {
          font-family: "Alibaba PuHuiTi";
          font-style: normal;
          font-weight: 500;
          font-size: 14px;
          line-height: 22px;
          color: #2c2c35;
          display: flex;
          justify-content: flex-start;
          align-items: center;
        }

        div {
          display: flex;
          justify-content: flex-start;

          .headline {
            color: #767684;
            width: 150px;
          }

          .headline-l {
            flex: 1;
          }
        }
      }
    }

    &-r {
      padding: 16px;
      // width: 860px;
      width: 100%;
      height: 426px;
      background: #ffffff;
      // box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
      border-radius: 8px;

      &-tab {
        margin-top: 12px;

        ::v-deep {
          .el-tabs__header {
            margin: 0 !important;
          }

          .el-tabs--card > .el-tabs__header {
            margin-left: 10px;
            border: 0;
          }

          .el-tabs--card > .el-tabs__header .el-tabs__item {
            height: 26px;
            padding: 0 8px;
            border: 0;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
            height: 26px;
            padding: 8px;
            background: #ffffff;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }

        ::v-deep .el-tabs__nav {
          padding: 0 8px;
          // min-width: 592px;
          height: 34px !important;
          border: 0;
          background: #f2f3f7;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: space-around;
          transform: translateX(0px) !important;
        }

        .box {
          margin-top: 16px;
          display: flex;
          justify-content: space-between;
          flex-direction: column;
          background: #ffffff !important;

          div {
            display: flex;
            margin-bottom: 16px;

            .headline {
              width: 120px;
              font-family: "Alibaba PuHuiTi";
              font-style: normal;
              font-weight: 400;
              font-size: 14px;
              line-height: 22px;
              color: #767684;
            }

            .headline-l {
              font-family: "Alibaba PuHuiTi";
              font-style: normal;
              font-weight: 400;
              font-size: 14px;
              line-height: 22px;
              color: #2c2c35;
              flex: 1;
            }
          }
        }
      }
    }
  }
}
</style>
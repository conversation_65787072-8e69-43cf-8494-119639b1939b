<template>
  <div class="manage">
    <div class="manage-port">
      <div
        v-for="(item, index) in portData"
        :key="index"
        :class="[
          'manage-port-item',
          item.task_id === Number(task_id) ? 'task-active' : '',
        ]"
      >
        <div class="manage-port-item-top">
          <div class="manage-port-item-top-title">{{ item.flow_name }}</div>
          <div class="line">
            <div class="line-start"></div>
            <div class="line-line"></div>
            <div class="line-end"></div>
          </div>
          <div
            class="manage-port-item-top-handle"
            v-show="!item.hasOwnProperty('isMock')"
          >
            <el-select
              v-model="item.task_id"
              style="width: 102px"
              placeholder="请选择"
              @change="(val) => TASK_CHANGE(val, item, index)"
            >
              <el-option
                v-for="x in options"
                :key="x.value"
                :label="x.label"
                :value="x.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="manage-port-item-bottom">
          <div class="manage-port-item-bottom-total">
            <div class="manage-port-item-bottom-total-num">
              {{ FMT_MBPS(item.num).num }}
            </div>
            <div class="manage-port-item-bottom-total-unit">
              {{ FMT_MBPS(item.num).unit }}
            </div>
          </div>
          <div class="manage-port-item-bottom-chart">
            <v-chart
              :option="item.echarts"
              autoresize
              :update-options="{ notMerge: true }"
            ></v-chart>
          </div>
        </div>
      </div>
    </div>
    <div v-loading="taskLoading" class="manage-task">
      <div
        v-for="(item, index) in taskData"
        :key="index"
        :class="[
          'manage-task-item',
          activeIndex === index ? 'task-active' : '',
        ]"
        :style="{
          'border-color': activeIndex === index ? '#116EF9' : '#ffffff',
        }"
        @click="CLICK_TASK(item, index)"
      >
        <div class="top">
          <div class="title">
            <div>
              <i
                :class="[
                  item.task_state === 1
                    ? 'icon-a-16_running'
                    : 'icon-a-16_stop2',
                  'iconfont',
                ]"
                :style="{
                  color: item.task_state === 1 ? '#116EF9' : '#767684',
                }"
              ></i>
            </div>
            <div class="name">
              {{ item.task_name }}
            </div>
            <div v-if="item.batch_remark" class="description">
              {{ item.batch_remark || "-" }}
            </div>
          </div>
          <div class="top-handle" v-show="!item.hasOwnProperty('isMock')">
            <div v-show="activeIndex === index" class="tab">
              <div
                :class="['item', item.task_state === 1 ? 'active' : '']"
                @click.stop="TASK_SWITH(1, index)"
              >
                运行
              </div>
              <div
                :class="['item', item.task_state === 2 ? 'active' : '']"
                @click.stop="TASK_SWITH(2, index)"
              >
                挂起
              </div>
            </div>
            <el-button
              v-show="activeIndex === index"
              size="mini"
              plain
              @click.stop="TASK_CONFIG(item)"
            >
              配置
            </el-button>
          </div>
        </div>
        <div class="content">
          <div class="content-item">
            <div class="content-item-label">流量大小：</div>
            <div class="content-item-value">
              <div class="num">{{ FMT_MBPS(item.bps || 0).num }}</div>
              <div class="unit">{{ FMT_MBPS(item.bps || 0).unit }}</div>
            </div>
          </div>
          <div class="content-item">
            <div class="content-item-label">全流量留存量：</div>
            <div class="content-item-value">
              <div class="num">{{ FMT_THOU(item.full_save).num }}</div>
              <div class="unit">{{ FMT_THOU(item.full_save).unit }}</div>
            </div>
          </div>
          <div class="content-item">
            <div class="content-item-label">采集规则：</div>
            <div class="content-item-value">
              <div class="num">{{ item.rule }}</div>
              <div class="unit">个</div>
            </div>
          </div>
          <div class="content-item">
            <div class="content-item-label">过滤规则：</div>
            <div class="content-item-value">
              <div class="num">{{ item.filter_out }}</div>
              <div class="unit">个</div>
            </div>
          </div>
          <div class="content-item">
            <div class="content-item-label">元数据留存量：</div>
            <div class="content-item-value">
              <div class="num">{{ FMT_THOU(item.pb_num).num }}</div>
              <div class="unit">{{ FMT_THOU(item.pb_num).unit }}</div>
            </div>
          </div>
          <div class="content-item">
            <div class="content-item-label">告警数量：</div>
            <div class="content-item-value">
              <div class="num">{{ item.alarm }}</div>
              <div class="unit">个</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <TaskConfig
      v-model="taskConfigDrawer"
      :task-config-data="taskConfigData"
      @updateTaskInfo="updateTaskInfo"
    />
    <Confirm
      v-model="confirmVisible"
      :content="confirmContent"
      @confirm="TASK_PUT"
    />
  </div>
</template>

<script>
import {
  mbps1_options,
  mbps2_options,
  mbps3_options,
  mbps4_options,
} from "./echartsData";
import { task_info, task_put, tz_check } from "@/api/MbpsData";
import TaskConfig from "./TaskConfig.vue";
export default {
  name: "Manage",
  components: {
    TaskConfig,
  },
  data() {
    return {
      taskConfigDrawer: false,
      taskLoading: false,
      taskConfigData: {},
      probeCheck: false,
      portData: [
        {
          isMock: true,
          id: 1,
          flow_name: "网口初始化中",
          pcie_id: "0000:13:00.0",
          task_id: 0,
          task_state: 0,
          echarts: {
            title: {},
            tooltip: { show: false },
            grid: {
              left: "0",
              right: "0",
              bottom: "15",
              top: "0",
            },
            xAxis: { type: "category", show: false },
            yAxis: { type: "value", show: false },
            series: [
              {
                type: "bar",
                stack: "Total",
                itemStyle: {
                  borderColor: "transparent",
                  color: "transparent",
                },
                data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
              },
              {
                type: "bar",
                stack: "Total",
                data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                itemStyle: {
                  normal: {
                    barBorderRadius: 2,
                    barWidth: 10,
                    color: "#767684", // 灰色表示异常
                    barGap: 50,
                  },
                },
              },
            ],
          },
          num: 0,
          _status: "error", // 自定义错误标识
          _message: "数据获取失败",
        },
      ],
      task_id: 0,
      activeIndex: 0,
      options: [
        {
          value: 0,
          label: "主任务",
        },
        {
          value: 1,
          label: "从任务",
        },
      ],
      taskData: [
        {
          task_id: "0",
          batch_id: 0,
          batch_remark: "",
          fullflow_state: "",
          full_flow_should_log_def: 0,
          parse_proto_should_log_def: 0,
          parse_proto_should_log_def_list: [],
          ddos_state: 0,
          task_state: 0,
          netflow_vos: [],
          time: "",
          filter_out: 0,
          rule: 0,
          full_save: 0,
          bps: 0,
          pb_num: 0,
          alarm: 0,
          lost_pbs: 0,
          handle: 0,
          all_bps: [],
          bps_out: [],
          task_name: "主任务",
          isMock: true,
        },
        {
          task_id: "1",
          batch_id: 0,
          batch_remark: "",
          fullflow_state: "",
          full_flow_should_log_def: 0,
          parse_proto_should_log_def: 0,
          parse_proto_should_log_def_list: [],
          ddos_state: 0,
          task_state: 0,
          netflow_vos: [],
          time: "",
          filter_out: 0,
          rule: 0,
          full_save: 0,
          bps: 0,
          pb_num: 0,
          alarm: 0,
          lost_pbs: 0,
          handle: 0,
          all_bps: [],
          bps_out: [],
          task_name: "从任务",
          isMock: true,
        },
      ],
      taskInfo: [],
      batch_remark: {
        0: "",
        1: "",
      },
      mbpsOptions: {
        0: mbps1_options,
        1: mbps2_options,
        2: mbps3_options,
        3: mbps4_options,
      },
      confirmVisible: false,
      confirmTitle: "",
      confirmContent: "",
      confirmState: "",
      confirmIndex: "",
    };
  },
  watch: {
    // 监听网口数据变化
    "$store.state.long.tableType18": {
      immediate: true,
      deep: true,
      handler: function (val) {
        if (!val || !val.task_flow || !val.task_flow[0]) {
          console.warn("tableType18未能正确推送数据");
          /* 设置默认portData */
          return;
        }
        let { flow, flow_status, task_flow, status } = val.task_flow[0];
        this.portData = flow;
        this.portData.forEach((item, index) => {
          // 1. 任务状态
          task_flow.forEach((flow, y) => {
            if (flow.netflow.length && flow.netflow.includes(item.flow_name)) {
              item.task_id = flow.task_id;
              item.task_state = flow.task_state;
            }
          });

          // 2. echart 图表
          this.mbpsOptions[index].series[1].data = flow_status[item.flow_name];
          // 判断对应网口绑定的任务渲染对应的的状态色
          this.mbpsOptions[index].series[1].itemStyle.normal.color =
            item.task_state === 1 ? "#07d4ab" : "#ff9534";
          item.echarts = this.mbpsOptions[index];

          // 3. 拼装网口流量
          status.forEach((state) => {
            if (item.flow_name === state.port_pos) {
              item.num = state.bps;
            }
          });
        });
      },
    },
    // 监听主、从任务数据变化(仅展示数据，不包括配置数据)
    "$store.state.long.tableType21": {
      immediate: true,
      deep: true,
      handler: function (val) {
        if (!val || !val.forensics) {
          console.warn("tableType21未推送或格式不正确");
          return;
        }
        if (this.taskInfo.length) {
          this.taskData = [];
          // 上合并taskInfo和taskData的数据
          this.taskInfo.forEach((info) => {
            val.forensics.forEach((data) => {
              if (info.task_id === Number(data.task_id)) {
                data.task_name = info.task_id ? "从任务" : "主任务";
                this.taskData.push({ ...info, ...data });
              }
            });
          });
        }
      },
    },
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.GET_TASK_INFO();
      this.PROBE_CHECK();
      this.GET_TASKID();
    },
    // 更新任务信息
    updateTaskInfo() {
      this.GET_TASK_INFO();
    },
    // 切换主任务
    CLICK_TASK(val, index) {
      if (val.hasOwnProperty("isMock")) return;
      localStorage.setItem("task_id", val.task_id);
      this.task_id = val.task_id;
      this.activeIndex = index;
      this.init();
      this.$emit("toggle");
    },
    // TASK_ID，区分主从任务，及更新一些数据
    GET_TASKID() {
      this.task_id = localStorage.getItem("task_id") || "0";
      this.activeIndex = Number(this.task_id);
    },
    // 获取主从任务、网口的基本数据并渲染线条
    GET_TASK_INFO() {
      this.probeCheck = true;
      task_info()
        .then((res) => {
          this.taskInfo = res.data;
          this.$store.commit("conversational/getCurrentTask", res.data);
          setTimeout(() => {
            this.probeCheck = false;
          });
        })
        .catch(() => {
          this.probeCheck = false;
        });
    },
    // 格式化网络速率
    FMT_THOU(size) {
      if (size === 0) {
        return {
          num: "0",
          unit: "B",
        };
      }
      let bytes = parseInt(size);
      if (bytes === 0) return "0 B";
      let k = 1024, // or 1024
        sizes = ["Byte", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"],
        i = Math.floor(Math.log(bytes) / Math.log(k));
      return {
        num: (bytes / Math.pow(k, i)).toPrecision(3),
        unit: sizes[i],
      };
    },
    // 格式化流量大小
    FMT_MBPS(size) {
      if (size === 0 || !size) {
        return {
          num: "0",
          unit: "bps",
        };
      }
      let bytes = parseInt(size);
      if (bytes === 0) return "0 B";
      let k = 1000, // or 1024
        sizes = ["bps", "Kbps", "Mbps", "Gbps"],
        i = Math.floor(Math.log(bytes) / Math.log(k));
      return {
        num: (bytes / Math.pow(k, i)).toPrecision(3),
        unit: sizes[i],
      };
    },
    // 变更任务状态
    TASK_SWITH(state, index) {
      this.confirmContent = "是否变更任务状态，服务重启可能导致部分展示延迟";
      this.confirmState = state;
      this.confirmIndex = index;
      this.confirmVisible = true;
    },
    TASK_PUT(state, index, localData) {
      let params;
      if (state && index && localData) {
        params = this.taskData.map((item, i) => {
          return {
            task_id: item.task_id,
            netflows: [],
            task_state: index === i ? state : item.task_state,
          };
        });
        localData.forEach((port) => {
          params.forEach((task) => {
            if (port.task_id === Number(task.task_id)) {
              task.netflows.push(port.id);
            }
          });
        });
      } else {
        params = this.taskData.map((item, i) => {
          return {
            task_id: item.task_id,
            netflows: [],
            task_state:
              this.confirmIndex === i ? this.confirmState : item.task_state,
          };
        });
        this.portData.forEach((port) => {
          params.forEach((task) => {
            if (port.task_id === Number(task.task_id)) {
              task.netflows.push(port.id);
            }
          });
        });
      }

      task_put(params).then((res) => {
        if (res.err === 0) {
          this.$message.success(res.msg);
          // 更新数据
          this.PROBE_CHECK(); // 重启探针
          this.GET_TASK_INFO(); // 重新获取任务信息
        }
      });
      this.confirmVisible = false;
    },
    // 变更网口状态
    TASK_CHANGE(state, item, index) {
      let localData = JSON.parse(JSON.stringify(this.portData));
      localData[index].task_id = state;
      item.task_id = state;
      this.$confirm(
        "变更网口状态，服务重启可能导致部分展示延迟",
        "是否变更网口状态",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          this.TASK_PUT(state, index, localData);
        })
        .catch(() => {
          console.log("取消");
        });
    },
    // 任务配置
    TASK_CONFIG(item) {
      this.taskConfigData = item;
      this.taskConfigDrawer = true;
    },
    // 查询探针同步状态
    PROBE_CHECK() {
      tz_check().then((res) => {
        switch (res.data) {
          case 60001:
            // 同步成功
            this.$message.success("探针同步成功");
            tz_check({ suc: 4 }).then((res) => {
              if (res.err === 0) {
                this.probeCheck = false;
                setTimeout(() => {
                  this.$emit("toggle");
                  this.init();
                });
              }
            });
            break;
          case 60002:
            // 同步失败
            this.$message.warning("探针同步失败");
            this.probeCheck = false;
            this.$emit("toggle");
            this.init();
            break;
          case 60003:
            // 同步中
            this.$message.info("探针同步中");
            this.probeCheck = true;
            setTimeout(() => {
              this.PROBE_CHECK();
              this.init();
            }, 5000);
            break;
          case 60004:
            // 静止状态，无同步任务
            this.probeCheck = false;
            break;
          default:
            break;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.manage {
  display: flex;
  flex-direction: column;
  &-port,
  &-task {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 120px;
    font-size: 14px;
  }
  &-port {
    &-item {
      padding: 16px;
      box-sizing: border-box;
      flex: 1;
      opacity: 0.5;
      border-radius: 8px;
      height: 100%;
      &:not(:first-child) {
        margin-left: 16px;
      }
    }
  }
  &-task {
    margin-top: 16px;
    font-size: 14px;
    &-item {
      box-sizing: border-box;
      flex: 1;
      opacity: 0.5;
      border-top: 2px solid #f2f3f7;
      height: 120px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      background: linear-gradient(
        180deg,
        #ffffff 0%,
        rgba(255, 255, 255, 0) 100%
      );
      &:hover {
        cursor: pointer;
      }
      .content {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        padding: 8px 16px;
        box-sizing: border-box;
        &-item {
          width: 33.3%;
          display: flex;

          font-size: 14px;
          line-height: 30px;
          &-label {
            color: #767684;
          }
          &-value {
            display: flex;
            .num {
              color: #2c2c35;
              font-weight: bold;
            }
            .unit {
              color: #767684;
              margin-left: 4px;
            }
          }
        }
      }
      .top {
        padding: 12px 16px 6px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        .title {
          display: flex;
          align-items: center;
          .name {
            margin-left: 6px;
            font-weight: bold;
          }
          .description {
            padding: 4px 6px;
            margin-left: 8px;
            width: fit-content;
            background: #f2f3f7;
            color: #767684;
            border-radius: 12px;
          }
        }
        &-handle {
          display: flex;
          .tab {
            display: flex;
            align-items: center;
            width: 86px;
            background: #f2f3f7;
            padding: 2px;
            margin-right: 8px;
            border-radius: 4px;
            box-sizing: border-box;
            .item {
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 42px;
              &:hover {
                cursor: pointer;
              }
            }
            .active {
              background: #fff;
              color: #116ef9;
              border-radius: 4px;
            }
          }
        }
      }
    }
  }
  .manage-port-item {
    min-width: 250px;
    flex: 1; // 保持弹性
    background: #fff;
    &-top {
      display: flex;
      align-items: center;
      justify-content: space-between;

      &-title {
        color: #767684;
      }
      .line {
        flex: 1;
        display: flex;
        margin: 0 12px;
        align-items: center;
        &-start {
          height: 10px;
          width: 10px;
          border: 1px solid #4a97ff;
          border-radius: 50%;
        }
        &-line {
          margin: 0 2px;
          flex: 1;
          height: 1px;
          border: 1px dashed #4a97ff;
        }
        &-end {
          height: 10px;
          width: 10px;
          border: 5px solid #4a97ff;
          border-radius: 50%;
        }
      }
    }
    &-bottom {
      margin-top: 14px;
      display: flex;
      &-total {
        &-num {
          font-size: 24px;
          font-weight: bold;
        }
        &-unit {
          color: #767684;
        }
      }
      &-chart {
        height: 50px;
        flex: 1;
      }
    }
  }
  .task-active {
    opacity: 1;
    background: #fff;
    .content-item-value {
      .num {
        color: #116ef9;
      }
    }
  }
}
</style>

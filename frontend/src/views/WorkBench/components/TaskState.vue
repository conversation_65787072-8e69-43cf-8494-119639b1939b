<template>
  <div class="task-box">
    <div class="task-state">
      <!-- 数据展示 -->
      <article class="box1">
        <div class="bag-box">
          <section class="bag">
            <div class="bag-title">
              <div>丢包率</div>
              <div></div>
            </div>
            <div class="bag-content">
              <div class="bag-content-label">
                <div>
                  <div></div>
                  <span
                    >丢包数&emsp;<span style="color: #000">{{
                      THOUSAND(bag_options.series[0].data[1].value)
                    }}</span
                    >&emsp;个</span
                  >
                </div>
                <div>
                  <div></div>
                  <span
                    >总包数&emsp;<span style="color: #000">{{
                      THOUSAND(
                        bag_options.series[0].data[0].value +
                          bag_options.series[0].data[1].value
                      )
                    }}</span
                    >&emsp;个</span
                  >
                </div>
              </div>
              <div class="bag-content-echarts">
                <el-progress
                  type="circle"
                  :percentage="bagPercent"
                  color="#FF9534"
                ></el-progress>
              </div>
            </div>
          </section>
          <section v-if="task_id == 0" class="stat">
            <div class="stat-title">
              <div>看板统计信息</div>
              <div></div>
            </div>
            <div class="stat-content">
              <div class="stat-content-item">
                <div class="label">采集规则命中数据量</div>
                <div class="value">
                  {{ FMT_THOU(stat[0].rule_pcap).num
                  }}{{ FMT_THOU(stat[0].rule_pcap).unit }}
                </div>
              </div>
              <div class="stat-content-item">
                <div class="label">过滤数据量</div>
                <div class="value">
                  {{ FMT_THOU(stat[0].filter_drop).num
                  }}{{ FMT_THOU(stat[0].filter_drop).unit }}
                </div>
              </div>
              <div class="stat-content-item">
                <div class="label">白名单过滤量</div>
                <div class="value">
                  {{ FMT_THOU(stat[0].rule_drop).num
                  }}{{ FMT_THOU(stat[0].rule_drop).unit }}
                </div>
              </div>
              <div class="stat-content-item">
                <div class="label">挂起次数</div>
                <div class="value">
                  {{ stat[0].suspend_times || "0" }}
                </div>
              </div>
              <div class="stat-content-item stat-content-time">
                <div class="label">初次启动时间</div>
                <div class="value">
                  {{ TIME_FMT(stat[0].start_time) || "0" }}
                </div>
              </div>
              <div class="stat-content-item">
                <div class="label">挂起时间</div>
                <div class="value">
                  {{ TIME_FMT(stat[0].run_time) || "0" }}
                </div>
              </div>
            </div>
          </section>
          <section v-else class="stat">
            <div class="stat-title">
              <div>看板统计信息</div>
              <div></div>
            </div>
            <div class="stat-content">
              <div class="stat-content-item">
                <div class="label">采集规则命中数据量</div>
                <div class="value">
                  {{ FMT_THOU(stat[1].rule_pcap).num
                  }}{{ FMT_THOU(stat[1].rule_pcap).unit }}
                </div>
              </div>
              <div class="stat-content-item">
                <div class="label">过滤数据量</div>
                <div class="value">
                  {{ FMT_THOU(stat[1].filter_drop).num
                  }}{{ FMT_THOU(stat[1].filter_drop).unit }}
                </div>
              </div>
              <div class="stat-content-item">
                <div class="label">白名单过滤量</div>
                <div class="value">
                  {{ FMT_THOU(stat[1].rule_drop).num
                  }}{{ FMT_THOU(stat[1].rule_drop).unit }}
                </div>
              </div>
              <div class="stat-content-item">
                <div class="label">挂起次数</div>
                <div class="value">
                  {{ stat[1].suspend_times || "0" }}
                </div>
              </div>
              <div class="stat-content-item stat-content-time">
                <div class="label">初次启动时间</div>
                <div class="value">
                  {{ TIME_FMT(stat[1].start_time) || "0" }}
                </div>
              </div>
              <div class="stat-content-item">
                <div class="label">挂起时间</div>
                <div class="value">
                  {{ TIME_FMT(stat[1].run_time) || "0" }}
                </div>
              </div>
            </div>
          </section>
        </div>
      </article>
      <!-- IP关系图 -->
      <article class="box2">
        <section class="ip">
          <div class="title" @click="IP_LINE">IP关系图</div>
          <v-chart
            v-if="ipChartShow"
            :option="ip_options"
            autoresize
            :update-options="{ notMerge: true }"
          >
          </v-chart>
          <div v-else class="mac-qs">
            <img src="../../../assets/images/mac-qs.png" alt="暂无数据" />
            <div>暂无数据</div>
          </div>
        </section>
      </article>

      <!-- MAC关系图 -->
      <article class="box3">
        <section class="mac">
          <div class="title">MAC关系图</div>
          <div class="mac-box">
            <v-chart
              v-if="macShow"
              :option="mac_options"
              autoresize
              :update-options="{ notMerge: true }"
            >
            </v-chart>
            <div v-else class="mac-qs">
              <img src="../../../assets/images/mac-qs.png" alt="暂无数据" />
              <div>暂无数据</div>
            </div>
          </div>
          <aside class="largen" @click="MAC_LARGEN">
            <svg-icon icon-class="largen" />
          </aside>
        </section>
      </article>
      <el-dialog
        title="MAC关系图"
        :visible.sync="macDialog"
        :before-close="MAC_LARGEN_OFF"
        custom-class="mac-dialog"
        :show-close="false"
        destroy-on-close
      >
        <template slot="title">
          <div>MAC关系图</div>
          <div>
            <el-button type="text" @click="MAC_LARGEN_OFF">关闭</el-button>
          </div>
        </template>
        <template>
          <v-chart
            :option="mac_options"
            autoresize
            :update-options="{ notMerge: true }"
          >
          </v-chart>
        </template>
      </el-dialog>
    </div>
    <!-- 任务流量折线图 -->
    <article class="box4">
      <div class="box4-title">
        <div class="box4-title-text">流量折线图</div>
        <div class="box4-title-handle">
          <aside>进</aside>
          <aside>出</aside>
          <Toggle
            v-model="taskLineType"
            :tab-data="[
              { key: 0, label: '实时' },
              { key: 1, label: '近7日' },
            ]"
          />
        </div>
      </div>
      <div class="box4-line">
        <v-chart
          v-if="!taskLineType"
          :option="taskline_options2"
          autoresize
          :update-options="{ notMerge: true }"
        >
        </v-chart>
        <v-chart
          v-else
          :option="taskline_options"
          autoresize
          :update-options="{ notMerge: true }"
        >
        </v-chart>
      </div>
    </article>
    <!-- IP网段列表 -->
    <intranet-list />
  </div>
</template>

<script>
import dayjs from "dayjs";
import {
  ip_options,
  mac_graph,
  mac_options,
  bag_options,
  taskline_options,
  taskline_options2,
  ip2_options,
} from "./echartsData";
import RelationGraph from "relation-graph";
import {
  communication_list,
  recent_flow,
  task_info,
  get_pool_list,
} from "@/api/TaskState";
import IntranetList from "./IntranetList.vue";
export default {
  // eslint-disable-next-line vue/no-unused-components
  components: { RelationGraph, IntranetList },
  data() {
    return {
      ipChartShow: false,
      // 所在任务ID
      task_id: 0,
      taskType: "",
      // IP图配置
      ip_options,
      ip2_options,

      // mac图配置
      mac_options,
      // 丢包率环图配置
      bag_options: {
        series: [
          {
            type: "pie",
            radius: ["50%", "70%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: "14",
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: 0, name: "总包数", itemStyle: { color: "#57EB92" } },
              { value: 0, name: "丢包数", itemStyle: { color: "#FFA048" } },
            ],
          },
        ],
      },
      // 任务流量折线图7天配置
      taskline_options,
      // 任务流量折线图实时配置
      taskline_options2,
      macDialog: false,

      // 任务流量折线图类型
      taskLineType: 0,
      tableData: [
        {
          date: "1",
          name: "王小虎",
          address: "上海市普陀区金沙江路 1518 弄",
        },
      ],
      // IP线条实例
      ipLines: [],
      // IP线条参数
      lineOptions: {
        // dash: { // 虚线样式
        //   animation: true,// 让线条滚动起来
        // },
        startPlug: "behind", // 线头
        endPlug: "behind", // 线尾
        color: "rgba(30, 130, 250, 0.5)",
        startPlugColor: "rgba(74,156,247,0.4)",
        endPlugColor: "rgba(86,229,152,0.4)",
        gradient: true,
        startSocket: "right", //在指引线开始的地方从元素左侧开始
        endSocket: "left", //在指引线开始的地方从元素右侧结束
        size: 14, // 线条尺寸
        path: "fluid", // 线条样式
        startSocketGravity: 50, // 线条初始拉力
        endSocketGravity: 50, // 线条结束拉力
      },
      ipLink: [
        {
          s: "node1",
          t: "node11",
        },
        {
          s: "node1",
          t: "node22",
        },
        {
          s: "node2",
          t: "node33",
        },
        {
          s: "node2",
          t: "node44",
        },
        {
          s: "node2",
          t: "node22",
        },
        {
          s: "node22",
          t: "node111",
        },
        {
          s: "node22",
          t: "node222",
        },
        {
          s: "node11",
          t: "node111",
        },
        {
          s: "node333",
          t: "node3333",
        },
      ],
      ipLinesTime: false,
      // 看板信息
      stat: [
        {
          // 规则留存大小
          rule_pcap: 0,
          // 过滤大小
          filter_drop: 0,
          // 白名单过滤大小
          rule_drop: 0,
          // 任务开始时间
          start_time: "",
          // 任务挂起时间
          run_time: "",
          // 任务挂起次数
          suspend_times: 0,
        },
        {
          // 规则留存大小
          rule_pcap: 0,
          // 过滤大小
          filter_drop: 0,
          // 白名单过滤大小
          rule_drop: 0,
          // 任务开始时间
          start_time: "",
          // 任务挂起时间
          run_time: "",
          // 任务挂起次数
          suspend_times: 0,
        },
      ],
      // 控制mac图是否缺省展示
      macShow: true,
      // 矿池IP列表
      miningipList: [],
      miningShow: [],
      miningPage: 1,
      miningTotal: 0,
    };
  },
  computed: {
    dict() {
      return this.$store.state.long.Dict;
    },
    // bag换图百分比
    bagPercent: function () {
      let a = this.$ACC_ADD(
        bag_options.series[0].data[1].value,
        bag_options.series[0].data[0].value
      );
      a = this.$ACC_DIV(bag_options.series[0].data[1].value, a);
      a = this.$ACC_MUL(a, 100);
      a = parseFloat(a.toFixed(2));
      if (isNaN(a)) {
        return 0;
      }
      return a;
    },
  },
  watch: {
    ipLinesTime: function (newData, oldData) {
      if (newData === true) {
        this.$nextTick(function () {
          this.IP_LINE();
        });
      }
    },
    "$store.state.long.tableType20": {
      immediate: true,
      handler: function (val, old) {
        if (!val.forensics) {
          console.warn("tableType20数据未正确推送");
          return;
        }
        this.stat = val.forensics;
      },
    },
    // mac关系图数据
    "$store.state.long.tableType13": {
      immediate: true,
      handler: function (val, old) {
        if (!val || !val.mac_list || !val.mac_list.length) {
          console.warn("tableType13未推送或格式不正确");
          this.macShow = false;
          return;
        }
        // 如果没有数据就不执行下面的逻辑
        if (!val.mac_list.length) return;
        let value = val;
        let data = [];
        let links = [];
        if (this.task_id == 0 && value.mac_list[0]["0"]) {
          data = value.mac_list[0]["0"].mac_list;
          links = value.mac_list[0]["0"].mac_mac_communication;
        } else if (this.task_id == 1 && value.mac_list[0]["1"]) {
          data = value.mac_list[0]["1"].mac_list;
          links = value.mac_list[0]["1"].mac_mac_communication;
        } else {
          data = [];
          links = [];
        }

        let newData = [];
        for (let i of data) {
          let obj = {
            id: i.mac,
            name: i.mac,
            // symbolSize: i.recv / 1024 / 1024,
            symbolSize: 5,
            // 厂商名称
            organization_name: i.organization_name,
            mac_inter: i.mac_inter === "intel_ip" ? "互联网" : "内网",
            ip: i.ip && i.ip.length > 0 ? i.ip[0].ip : "-",
            mask: i.ip && i.ip.length > 0 ? i.ip[0].mask : "-",
            value: i.recv,
          };
          newData.push(obj);
        }
        let newLinks = [];
        for (let i of links) {
          let obj = {
            source: i.mac_1,
            target: i.mac_2,
          };
          newLinks.push(obj);
        }
        mac_options.series[0].data = newData;
        mac_options.series[0].links = newLinks;
        if (newData.length < 1 || newLinks.length < 1) {
          this.macShow = false;
        } else {
          this.macShow = true;
        }
      },
    },
    // 丢包率
    "$store.state.long.tableType21": {
      immediate: true,
      handler: function (val, old) {
        if (!val || !val.forensics || !Array.isArray(val.forensics)) {
          console.warn("tableType21数据未推送或格式不正确");
          this.bag_options = {
            series: [
              {
                type: "pie",
                radius: ["50%", "70%"],
                avoidLabelOverlap: false,
                label: {
                  show: false,
                  position: "center",
                },
                emphasis: {
                  label: {
                    show: true,
                    fontSize: "14",
                    fontWeight: "bold",
                  },
                },
                labelLine: {
                  show: false,
                },
                data: [
                  { value: 0, name: "总包数", itemStyle: { color: "#57EB92" } },
                  { value: 0, name: "丢包数", itemStyle: { color: "#FFA048" } },
                ],
              },
            ],
          };
        }
        if (this.task_id == 0) {
          if (!val || !val.forensics || !Array.isArray(val.forensics)) {
            /* mock数据 */
            /* 丢包 */
            this.bag_options.series[0].data[1].value = 0;
            /* 总包 */
            this.bag_options.series[0].data[0].value = 0;
            /* 折线图 */
            this.taskline_options2.series[1].data = [
              0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            ];
            return;
          }
          // 丢包
          // this.bag_options.series[0].data[1].value = val.forensics[0].lost_pbs
          this.bag_options.series[0].data[1].value = val.forensics[0].lost_pbs;
          // 总包
          // this.bag_options.series[0].data[0].value = parseInt(val.forensics[0].handle)
          this.bag_options.series[0].data[0].value = parseInt(
            val.forensics[0].handle
          );
          // 任务流量折线图实时数据
          // 任务流量折线图实时数据
          if (
            this.$store.state.long.tableType21.forensics[1].all_bps.length > 0
          ) {
            let nodes = [];
            let times = [];
            this.$store.state.long.tableType21.forensics[1].all_bps.forEach(
              (i) => {
                nodes.push(i.bps);
                times.push(dayjs(i.time * 1000).format("HH:mm:ss"));
              }
            );
            this.taskline_options2.series[0].data = nodes;
            // this.taskline_options2.series[2].data = nodes
            this.taskline_options2.xAxis[0].data = times;
          }
          if (
            this.$store.state.long.tableType21.forensics[1].bps_out.length > 0
          ) {
            let nodes = [];
            let times = [];
            this.$store.state.long.tableType21.forensics[1].bps_out.forEach(
              (i) => {
                nodes.push(i.bps);
                times.push(dayjs(i.time * 1000).format("HH:mm:ss"));
              }
            );
            this.taskline_options2.series[1].data = nodes;
          }
        }
        if (this.task_id == 1) {
          // 丢包
          this.bag_options.series[0].data[1].value = val.forensics[1].lost_pbs;

          // 总包
          this.bag_options.series[0].data[0].value = parseInt(
            val.forensics[1].handle
          );

          // 任务流量折线图实时数据
          if (
            this.$store.state.long.tableType21.forensics[1].all_bps.length > 0
          ) {
            let nodes = [];
            let times = [];
            this.$store.state.long.tableType21.forensics[1].all_bps.forEach(
              (i) => {
                nodes.push(i.bps);
                times.push(dayjs(i.time * 1000).format("HH:mm:ss"));
              }
            );
            this.taskline_options2.series[0].data = nodes;
            // this.taskline_options2.series[2].data = nodes
            this.taskline_options2.xAxis[0].data = times;
          }
          if (
            this.$store.state.long.tableType21.forensics[1].bps_out.length > 0
          ) {
            let nodes = [];
            let times = [];
            this.$store.state.long.tableType21.forensics[1].bps_out.forEach(
              (i) => {
                nodes.push(i.bps);
                times.push(dayjs(i.time * 1000).format("HH:mm:ss"));
              }
            );
            this.taskline_options2.series[1].data = nodes;
            // this.taskline_options2.series[3].data = nodes
          }
        }
      },
    },
  },
  created() {
    this.GET_TASKID();
    this.GET_IP_DATA();
    this.GET_IP_STAGE();
  },
  mounted() {},
  destroyed() {
    this.REMOVE_LINE();
  },
  methods: {
    // TASK_ID，区分主从任务
    GET_TASKID() {
      this.task_id = localStorage.getItem("task_id") || "0";
      if (this.task_id == 0) {
        this.taskType = "主任务";
      } else if (this.task_id == 1) {
        this.taskType = "从任务";
      } else {
        this.taskType = "-";
      }
    },

    // 获取IP图数据
    GET_IP_DATA() {
      communication_list({
        page: 1,
        limit: 10,
        asc: false,
        task_id: [this.task_id],
        sys_type: 0,

        query: [],
        is_front_Page: true,
      })
        .then((res) => {
          if (res.err === 0) {
            this.ipChartShow = true;
            this.FORMAT_SANKEY(res.data.records);
          }
        })
        .catch((err) => {});
    },
    TABLE_INDEX(index) {
      return (this.miningPage - 1) * 10 + (index + 1);
    },
    // 获取矿池IP列表数据
    GET_MINING_IPLIST() {
      get_pool_list().then((res) => {
        if (res.err === 0) {
          this.miningipList = res.data.result;
          this.miningTotal = res.data.count;
          this.MINING_PAGE_CHANGE(1);
        }
      });
    },
    MINING_PAGE_CHANGE(val) {
      let start = val * 10 - 10;
      let end = val * 10;
      this.miningPage = val;
      this.miningShow = this.miningipList.slice(start, end);
    },
    // 判断mac关系图是否该展示
    CHECK_MAC_SHOW() {},
    // 序列化sankey图数据
    FORMAT_SANKEY(res) {
      console.time("sankey");
      let nodesObj = {};
      let links = [];
      let numSrcIP = 0;
      let numDstIP = 0;
      //let numCnt = 0;
      let numDstPort = 0;
      let numAppId = 0;
      for (let item of res) {
        let dst_ipFlag = item.d_ip + " ";
        // let dst_ipFlag = item.dst_ip;
        let src_ipFlag = item.s_ip;
        let cnt_Flag = item.cnt;
        // let src_ipFlag = item.src_ip;
        let dst_portFlag = "端口: " + item.dst_port;
        // let dst_portFlag = item.dst_port;
        let app_idFlag = "应用: " + item.app_id;
        if (item.app_id || item.app_id == 0)
          if (this.dict.app_value[item.app_id]) {
            app_idFlag = this.dict.app_value[item.app_id];
          } else {
            app_idFlag = "未知应用-" + item.app_id;
          }

        if (!nodesObj[dst_ipFlag]) {
          nodesObj[dst_ipFlag] = true;
          numSrcIP += 1;
        }
        if (!nodesObj[dst_portFlag]) {
          nodesObj[dst_portFlag] = true;
          numDstPort += 1;
        }
        if (!nodesObj[app_idFlag]) {
          nodesObj[app_idFlag] = true;
          numAppId += 1;
        }
        if (!nodesObj[src_ipFlag]) {
          nodesObj[src_ipFlag] = true;
          numDstIP += 1;
        }
        // if (!nodesObj[cnt_ipFlag]) {
        //   nodesObj[cnt_ipFlag] = true;
        //   numCnt += 1;
        // }
        // 填充links
        let index = this.SANKEY_LINKS(links, src_ipFlag, app_idFlag);
        if (index < 0) {
          links.push({
            source: src_ipFlag,
            target: app_idFlag,
            value: cnt_Flag,
          });
        } else {
          links[index].value += cnt_Flag;
        }
        index = this.SANKEY_LINKS(links, app_idFlag, dst_portFlag);
        if (index < 0) {
          links.push({
            source: app_idFlag,
            target: dst_portFlag,
            value: cnt_Flag,
          });
        } else {
          links[index].value += cnt_Flag;
        }
        index = this.SANKEY_LINKS(links, dst_portFlag, dst_ipFlag);
        if (index < 0) {
          links.push({
            source: dst_portFlag,
            target: dst_ipFlag,
            value: cnt_Flag,
          });
        } else {
          links[index].value += cnt_Flag;
        }
      }
      let nodes = [];
      for (let key in nodesObj) {
        nodes.push({
          name: key,
        });
      }

      this.ip_options.series[0].data = nodes;
      this.ip_options.series[0].links = links;
      console.timeEnd("sankey");
    },
    // 绘制sankey图连线
    SANKEY_LINKS(array, source, target) {
      let len = array.length;
      for (let i = 0; i < len; i++) {
        if (array[i].source === source && array[i].target === target) {
          return i;
        }
      }
      return -1;
    },
    // 获取IP网段列表图数据（HTTP请求方式）
    GET_IP_STAGE() {
      recent_flow({
        days: 7,
        task_id: this.task_id,
      }).then((res) => {
        if (res.data.length > 0) {
          let arr = [];
          let arr2 = [];
          res.data.forEach((i) => {
            arr.unshift(i.bps);
            arr2.unshift(dayjs(i.time * 1000).format("YYYY-MM-DD HH:mm:ss"));
          });
          this.taskline_options.series[0].data = arr;
          // this.taskline_options.series[1].data = arr
          this.taskline_options.xAxis[0].data = arr2;
        }
      });
    },
    // 刷新页面IP线条位置
    POSITION_LINE() {
      console.warn("leader-line：position");
      let len = this.ipLines.length;
      for (let i = 0; i < len; i++) {
        this.ipLines[i].position();
      }
    },
    // 销毁当前页面已有IP线条
    REMOVE_LINE() {
      if (this.ipLines.length > 1) {
        let len = this.ipLines.length;
        for (let i = 0; i < len; i++) {
          this.ipLines[i].remove();
        }
        this.ipLines = [];
        console.warn("leader-line：remove");
      }
    },
    // 创建IP解析图连线
    IP_LINE() {
      // let len = this.ipLink.length;
      // for (let i = 0; i < len; i++) {
      //   let line = LeaderLine.setLine(document.getElementById(this.ipLink[i].s), document.getElementById(this.ipLink[i].t), this.lineOptions)
      //   this.ipLines.push(line)
      // }
      // console.log('创建连线')
    },
    // ip协议格式化
    TCP_FORMATTER(data) {},
    // 放大mac关系图
    MAC_LARGEN() {
      this.macDialog = true;
      this.mac_options.series[0].zoom = 1.0;
      this.mac_options.series[0].roam = true;
    },
    // 全屏mac关系图关闭
    MAC_LARGEN_OFF() {
      this.mac_options.series[0].zoom = 0.8;
      this.mac_options.series[0].roam = false;
      this.macDialog = false;
    },

    // 格式化时间
    TIME_FMT(date) {
      // eslint-disable-next-line use-isnan
      if (!date || date == NaN) {
        return "-";
      }
      return dayjs(date * 1000).format("YYYY-MM-DD HH:mm:ss");
    },
    // 格式化网络速率
    FMT_MBPS(size) {
      if (size === 0) {
        return {
          num: "0",
          unit: "",
        };
      }
      let bytes = parseInt(size);
      if (bytes === 0) return "0 B";
      let k = 1000, // or 1024
        sizes = ["bps", "Kbps", "Mbps", "Gbps"],
        i = Math.floor(Math.log(bytes) / Math.log(k));
      // return (bytes / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i];
      return {
        num: (bytes / Math.pow(k, i)).toPrecision(3),
        unit: sizes[i],
      };
    },
    // 格式化流量大小
    FMT_THOU(size) {
      if (size === 0) {
        return {
          num: "0",
          unit: "",
        };
      }
      let bytes = parseInt(size);
      if (bytes === 0) return "0 B";
      let k = 1024, // or 1024
        sizes = ["Byte", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"],
        i = Math.floor(Math.log(bytes) / Math.log(k));
      return {
        num: Number((bytes / Math.pow(k, i)).toPrecision(3)).toFixed(2),
        unit: sizes[i],
      };
    },
    // 千分位加逗号
    THOUSAND(num) {
      return num.toLocaleString();
    },
    // 复制域名
    DOMAIN_COPY(text) {
      navigator.clipboard.writeText(text);
      this.$message.success(`${text}已复制到剪切板`);
    },
  },
};
</script>

<style lang="scss" scoped>
.task-box {
  width: 100%;
  height: auto;

  .task-state {
    width: 100%;
    height: 424px;
    box-sizing: border-box;
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;

    > article {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    section {
      background: #f7f8fa;
      border-radius: 4px;
      box-sizing: border-box;
    }

    .title {
      width: 100%;
      font-size: 14px;
      font-weight: 700;
      text-align: center;
    }

    .box1 {
      width: 23%;
      height: 100%;

      .bag-box {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .bag {
          width: 100%;
          height: 114px;
          position: relative;
          display: flex;
          padding: 12px 9px 9px 16px;
          box-sizing: border-box;
          flex-direction: column;
          position: relative;
          background: #fffaf5;
          z-index: 0;
          &::before {
            position: absolute;
            background: #ff9534;
            content: "";
            left: 0px;
            top: 14px;
            width: 2px;
            height: 12px;
            z-index: 1;
          }

          &-title {
            width: 100%;
            height: 18px;
            font-size: 14px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            > div:nth-of-type(1) {
              font-size: 14px;
              color: #000;
              font-weight: bold;
            }

            > div:nth-of-type(2) {
              font-size: 14px;
              color: #116ef9;
              cursor: pointer;
            }
          }

          &-content {
            width: 100%;
            height: 80px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-sizing: border-box;

            &-label {
              width: 180px;
              height: 100%;
              display: flex;
              flex-direction: column;
              justify-content: space-evenly;

              > div {
                color: #767684;
                font-size: 14px;
                display: flex;
                align-items: center;
              }

              > div:nth-of-type(1) {
                > div {
                  width: 5px;
                  height: 5px;
                  border-radius: 50%;
                  background-color: #ffa048;
                  margin-right: 9px;
                }
              }

              > div:nth-of-type(2) {
                > div {
                  width: 5px;
                  height: 5px;
                  border-radius: 50%;
                  background-color: #57eb92;
                  margin-right: 9px;
                }
              }
            }

            &-echarts {
              width: 75px;
              height: 75px;
              display: flex;
              justify-content: center;
              align-items: center;
              position: relative;
              margin-right: 10px;

              ::v-deep .el-progress-circle {
                width: 100% !important;
              }

              > div {
                width: 100%;
                height: 100%;
                z-index: 700;
              }

              &-aside {
                width: 67px;
                height: 67px;
                position: absolute;
                border: 2px solid #ebebeb;
                border-radius: 50%;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 699;

                > div {
                  width: 50px;
                  height: 50px;
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  background-color: #fff;
                  border-radius: 50%;
                  box-shadow: 0px 10px 10px rgba(0, 0, 0, 0.04);
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  color: #2b2b2b;
                  font-size: 14px;
                  font-weight: bold;
                }
              }
            }
          }
        }

        .stat {
          width: 100%;
          height: 294px;
          position: relative;
          display: flex;
          padding: 12px 9px 9px 16px;
          box-sizing: border-box;
          flex-direction: column;
          position: relative;
          z-index: 0;
          &::before {
            position: absolute;
            background: #116ef9;
            content: "";
            left: 0px;
            top: 14px;
            width: 2px;
            height: 12px;
            z-index: 1;
          }

          &-title {
            width: 100%;
            height: 18px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            > div:nth-of-type(1) {
              font-size: 14px;
              color: #000;
              font-weight: bold;
            }

            > div:nth-of-type(2) {
              font-size: 14px;
              color: #116ef9;
              cursor: pointer;
            }
          }

          &-content {
            width: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin-top: 20px;
            flex-wrap: wrap;
            &-item {
              display: flex;
              justify-content: space-between;
              font-size: 14px;
              height: 36px;
              .label {
                color: #767684;
              }
              .value {
                color: #2c2c35;
                font-weight: 600;
              }
            }
            &-time {
              margin-top: 20px;
            }
          }
        }
      }
    }

    .box2 {
      width: 45%;
      height: 100%;
      position: relative;

      .ip {
        width: 100%;
        height: 100%;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        overflow-x: auto;
        position: relative;
        z-index: 0;
        .mac-qs {
          > div {
            background: none !important;
            border: none !important;
            box-shadow: none !important;
          }
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          align-items: center;

          > img {
            width: 220px;
            height: 155px;
            object-fit: cover;
          }

          > div {
            color: #2c2c35;
            font-size: 14px;
          }
        }
        &::before {
          position: absolute;
          background: #116ef9;
          content: "";
          left: 0px;
          top: 14px;
          width: 2px;
          height: 12px;
          z-index: 1;
        }

        .title {
          text-align: left;
          margin-top: 12px;
          padding-left: 10px;
        }

        > div {
          width: auto;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          margin-top: 5px;

          > div {
            width: 90px;
            height: 48px;
            // border: 1px solid red;
            // margin-right: 127px;
          }
        }

        > div:nth-of-type(1) {
          // margin-left: 18px;
          > div {
            background-color: #ffffff;
            height: 30px;
            border-radius: 5px;
            box-shadow: 0px 14.1262px 35.301px rgba(108, 73, 172, 0.2);
            display: flex;
            align-items: center;
            justify-content: space-evenly;
            flex-direction: column;
            color: #000;
            font-weight: 600;
          }
        }

        > div:nth-of-type(2) {
          > div {
            background: linear-gradient(
              69.96deg,
              #57eb92 6.14%,
              #39d979 98.54%
            );
            box-shadow: 0px 20px 50px rgba(87, 235, 146, 0.4);
            border-radius: 5px;
            display: flex;
            align-content: space-between;
            justify-content: space-evenly;
            flex-direction: column;
            padding-left: 14px;

            > span {
              color: #ffffff;
            }

            > span:nth-of-type(1) {
              font-size: 8px;
            }

            > span:nth-of-type(2) {
              font-weight: 600;
            }
          }
        }

        > div:nth-of-type(3) {
          > div {
            background: linear-gradient(
              69.96deg,
              #00d8d8 6.14%,
              #4a97ff 98.54%
            );
            box-shadow: 0px 20px 50px rgba(0, 216, 216, 0.4);
            border-radius: 5px;
            display: flex;
            align-content: space-between;
            justify-content: space-evenly;
            flex-direction: column;
            padding-left: 14px;

            > span {
              color: #ffffff;
            }

            > span:nth-of-type(1) {
              font-size: 8px;
            }

            > span:nth-of-type(2) {
              font-weight: 600;
            }
          }
        }

        > div:nth-of-type(4) {
          > div {
            height: 30px;
            background: linear-gradient(
              85.92deg,
              #e69eff -1.1%,
              #d765ff 99.72%
            );
            box-shadow: 0px 30px 50px rgba(230, 158, 255, 0.4);
            border-radius: 5px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #ffffff;
            font-weight: 500;
          }
        }
      }
    }

    .box3 {
      width: 30%;
      height: 100%;

      .mac {
        width: 100%;
        height: 100%;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        background: #f7f8fa;
        border-radius: 8px;
        position: relative;
        z-index: 0;
        &::before {
          position: absolute;
          background: #116ef9;
          content: "";
          left: 0px;
          top: 14px;
          width: 2px;
          height: 12px;
          z-index: 1;
        }

        > div:nth-of-type(1) {
          margin-top: 12px;
          text-align: left;
          padding-left: 10px;
          box-sizing: border-box;
        }

        .mac-box {
          width: 100%;
          height: 100%;
          z-index: 701;
          position: absolute;
          padding-top: 30px;
          box-sizing: border-box;

          .mac-qs {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
            align-items: center;

            > img {
              width: 220px;
              height: 155px;
              object-fit: cover;
            }

            > div {
              color: #2c2c35;
              font-size: 14px;
            }
          }
        }

        .mac-bg {
          width: 250px;
          height: 250px;
          position: absolute;
          z-index: 697;
          border: 1px solid rgb(245, 244, 244);
          border-radius: 50%;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background-color: #fff;
          box-shadow: 0px 46.1847px 36.9478px rgba(108, 73, 172, 0.1);
          box-sizing: border-box;

          .bg2 {
            width: 145px;
            height: 145px;
            position: absolute;
            z-index: 698;
            border: 1px solid rgb(245, 244, 244);
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: #fff;
            box-shadow: 0px 46.1847px 36.9478px rgba(108, 73, 172, 0.12);
            opacity: 1;

            .op2 {
              width: 100%;
              height: 50%;
              position: absolute;
              top: -10px;
              background: linear-gradient(
                white,
                rgba(255, 255, 255, 0.9),
                rgba(255, 255, 255, 0.41)
              );
            }

            .bg3 {
              width: 93px;
              height: 93px;
              position: absolute;
              z-index: 699;
              border: 1px solid rgb(245, 244, 244);
              border-radius: 50%;
              top: 50%;
              left: 50%;
              box-sizing: border-box;
              transform: translate(-50%, -50%);
              background-color: #fff;
              box-shadow: 0px 46.1847px 36.9478px rgba(108, 73, 172, 0.15);
              background: linear-gradient(
                rgba(255, 255, 255, 0),
                rgba(255, 255, 255, 1)
              );

              .op3 {
                width: 100%;
                height: 50%;
                position: absolute;
                top: -10px;
                background: linear-gradient(
                  white,
                  rgba(255, 255, 255, 0.9),
                  rgba(255, 255, 255, 0.41)
                );
              }
            }
          }
        }

        .line {
          width: 250px;
          height: 1px;
          background-color: #f1f1f1;
          opacity: 0.8;
          transform-origin: bottom center;
          top: 50%;
          z-index: 700;
        }

        #line1 {
          transform: rotateZ(0deg) scale(1);
        }

        #line2 {
          transform: rotateZ(60deg) scale(1);
        }

        #line3 {
          transform: rotateZ(60deg) scale(1);
        }

        .largen {
          width: 30px;
          height: 30px;
          position: absolute;
          right: 11px;
          bottom: 13px;
          background-color: #fff;
          box-shadow: 0px 8.4362px 22.349px rgba(108, 73, 172, 0.3);
          border-radius: 5.2349px;
          font-size: 14px;
          color: #5a5a89;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          z-index: 702;
        }
      }
    }

    .box3-1 {
      width: 30%;
      height: 100%;
      justify-content: flex-start;

      .title {
        width: 100%;
        height: 28px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        > div:nth-of-type(1) {
          color: #2c2c35;
          font-size: 14px;
        }
      }

      .table {
        width: 100%;
        border: 1px solid #f2f3f7;
        border-radius: 8px;

        ::v-deep .el-table td,
        .el-table th {
          padding: 5.5px 0;
          font-size: 14px;
        }

        ::v-deep .el-table__header-wrapper {
          border-radius: 0;

          .el-table__header th {
            padding: 10px 0;
          }
        }

        .copy {
          display: none;
          margin-right: 4px;
        }

        td:hover {
          .copy {
            display: inline-block;
            cursor: pointer;
          }
        }
      }
    }

    ::v-deep .mac-dialog {
      width: 1248px;
      height: 682px;
      margin-top: 56px !important;
      margin-left: 174px;
      border-radius: 8px;

      .el-dialog__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;

        > div:nth-of-type(1) {
          color: #000;
          font-size: 14px;
          font-weight: bold;
        }

        > div:nth-of-type(2) {
          .el-button--text {
            font-size: 14px;
            color: #116ef9;
          }
        }
      }

      .el-dialog__body {
        width: 100%;
        height: calc(100% - 100px);
        padding: 0;
      }
    }
  }

  .box4 {
    width: 100%;
    height: 230px;
    margin-top: 22px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &-title {
      width: 100%;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;

      &-text {
        color: #2b2b2b;
        font-size: 14px;
        font-weight: bold;
      }

      &-handle {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: bold;
        color: #2b2b2b;

        > aside {
          position: relative;
          margin-right: 50px;
        }
        aside:nth-child(2) {
          margin-right: 30px;
        }

        > aside::before {
          position: absolute;
          left: -34px;
          top: 8px;
          width: 24px;
          height: 2px;
          content: "";
          display: inline-block;
          background-color: #3c8fff;
        }

        > aside:nth-of-type(2)::before {
          position: absolute;
          left: -34px;
          top: 8px;
          width: 24px;
          height: 2px;
          display: inline-block;
          background-color: #4fde4d;
          border-radius: 8px;
          margin-right: 6px;
        }

        ::v-deep .el-radio {
          font-size: 14px;
          color: #2b2b2b;
          font-weight: bold;

          .el-radio__label {
            width: 36px;
            height: 27px;
            padding: 6px;
          }

          .el-radio__inner {
            display: none;
          }
        }

        ::v-deep.el-radio__input.is-checked + .el-radio__label {
          background-color: #116ef9;
          border-radius: 4px;
          width: 36px;
          height: 27px;
          color: #ffffff;
          padding: 6px;
        }
      }
    }

    &-line {
      width: 100%;
      height: 196px;
      display: flex;
      justify-content: center;
      padding-right: 50px;
      box-sizing: border-box;
    }
  }

  .box5 {
    width: 100%;
    height: 400px;
    margin-top: 22px;
    display: flex;
    flex-direction: column;

    &-title {
      width: 100%;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;

      &-text {
        color: #2b2b2b;
        font-size: 14px;
        font-weight: bold;
      }
    }
  }

  ::v-deep .rel-node-peel {
    padding: 0 !important;
  }
}
</style>

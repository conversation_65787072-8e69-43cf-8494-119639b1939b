<template>
  <div class="addrulr-box">
    <el-drawer
      id="addRules"
      ref="drawer"
      :visible.sync="dialog"
      direction="rtl"
      custom-class="drawer-box"
      :show-close="false"
      :with-header="false"
      :modal="false"
      :wrapper-closable="false"
      destroy-on-close
      :modal-append-to-body="false"
      @open="changeShowFormat"
      @close="closedrawer"
    >
      <div v-loading="loading" style="height: 100%">
        <div class="drawer-box-top">
          <div class="drawer-box-top-title">添加规则</div>
          <div class="drawer-box-top-left">
            <i class="el-icon-close" @click="closedrawer"></i>
          </div>
        </div>
        <div class="drawer-box-down">
          <div class="drawer-box-down-l">
            <div class="step">
              <div class="step-box">
                <div class="Dot1" @click="opencollapse('1')">
                  <div>
                    <span :class="step == '1' ? 'active' : 'textdefault'"
                      >1</span
                    >
                    <svg-icon
                      :icon-class="step == '1' ? 'stepicon2' : 'stepicon'"
                      class="icon1"
                    />
                  </div>
                  <div>
                    <div
                      style="
                        width: 14px;
                        height: 14px;
                        border-radius: 50%;
                        margin-right: 5px;
                      "
                      :class="step == '1' ? 'active' : 'default'"
                    ></div>
                    <div>填写基础信息</div>
                  </div>
                </div>
                <div class="Dot2" @click="opencollapse('2')">
                  <div>
                    <span :class="step == '2' ? 'active' : 'textdefault'"
                      >2</span
                    >
                    <svg-icon
                      class="icon2"
                      :icon-class="step == '2' ? 'stepicon2' : 'stepicon'"
                    />
                  </div>
                  <div
                    style="
                      width: 14px;
                      height: 14px;
                      border-radius: 50%;
                      margin-right: 5px;
                    "
                    :class="step == '2' ? 'active' : 'default'"
                  ></div>
                  <div>添加规则集</div>
                </div>
                <div class="Dot3" @click="opencollapse('3')">
                  <div>
                    <span :class="step == '3' ? 'active' : 'textdefault'"
                      >3</span
                    >
                    <svg-icon
                      class="icon3"
                      :icon-class="step == '3' ? 'stepicon2' : 'stepicon'"
                    />
                  </div>
                  <div
                    style="
                      width: 14px;
                      height: 14px;
                      border-radius: 50%;
                      margin-right: 5px;
                    "
                    :class="step == '3' ? 'active' : 'default'"
                  ></div>
                  <div>
                    程序响应<span style="color: #116ef9">（可选）</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="drawer-box-down-r">
            <el-form
              ref="ruleForm"
              :model="ruleForm"
              :rules="rules"
              class="demo-ruleForm"
            >
              <el-collapse v-model="activeNames" @change="handleChange">
                <el-collapse-item name="1" :disabled="collapse1">
                  <span slot="title" class="collapse-title"
                    >step1.基础信息</span
                  >
                  <div class="BasicIfo">
                    <div class="BasicIfo-top">
                      <div class="color:#000">
                        <el-form-item prop="rule_name" label="规则名称">
                          <el-input
                            v-model="ruleForm.rule_name"
                            placeholder="仅支持英文、数字和下划线"
                          ></el-input>
                        </el-form-item>
                      </div>
                      <div>
                        <el-form-item prop="capture_mode" label="采集模式">
                          <el-select
                            v-model="ruleForm.capture_mode"
                            placeholder="请选择"
                          >
                            <el-option
                              v-for="item in capturemode"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                            >
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </div>
                      <div>
                        <el-form-item prop="buffer_capacity" label="留存总量">
                          <el-input
                            v-model="ruleForm.buffer_capacity"
                            placeholder="仅支持数字"
                            type="number"
                            min="0"
                          >
                            <template #append>GB</template>
                          </el-input>
                        </el-form-item>
                      </div>
                    </div>
                    <div class="BasicIfo-center">
                      <el-form-item prop="rule_level" label="规则等级">
                        <el-radio-group
                          v-model="ruleForm.rule_level"
                          class="BasicIfo-center-radioStyle"
                        >
                          <el-radio label="15">可疑</el-radio>
                          <el-radio label="45">低危</el-radio>
                          <el-radio label="70">中危</el-radio>
                          <el-radio label="85">高危</el-radio>
                          <el-radio label="95">极危</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </div>
                    <div class="BasicIfo-down">
                      <div>规则描述</div>
                      <div>
                        <el-form-item prop="rule_desc">
                          <el-input
                            v-model="ruleForm.rule_desc"
                            placeholder="请输入你想要的描述"
                          ></el-input>
                        </el-form-item>
                      </div>
                    </div>
                  </div>
                  <!-- <div class="advancedmenu" @click="showmenu">
                    <div class="advancedmenu-box">
                      <span :class="activemenu ? 'activestyle' : ''">高级菜单</span>
                      <svg-icon v-if="!activemenu" icon-class="menu-up" />
                      <template v-if="activemenu">
                        <svg-icon icon-class="menu-down" />
                      </template>
                    </div>
                  </div> -->
                  <div v-if="activemenu" class="menubox">
                    <div class="menubox-radio">
                      <div class="menubox-radio-flow">
                        <div class="menubox-radio-flow-title">流量响应</div>
                        <div class="menubox-radio-flow-option">
                          <el-form-item prop="pcap_drop">
                            <el-radio v-model="ruleForm.pcap_drop" label="0">
                              留存
                            </el-radio>
                            <el-radio v-model="ruleForm.pcap_drop" label="1">
                              丢弃
                            </el-radio>
                          </el-form-item>
                        </div>
                      </div>
                      <div class="menubox-radio-log" prop="pb_drop">
                        <div class="menubox-radio-log-title">日志响应</div>
                        <div class="menubox-radio-log-option">
                          <el-form-item>
                            <el-radio v-model="ruleForm.pb_drop" label="0">
                              留存
                            </el-radio>
                            <el-radio v-model="ruleForm.pb_drop" label="1">
                              丢弃
                            </el-radio>
                          </el-form-item>
                        </div>
                      </div>
                    </div>
                    <div class="menubox-input">
                      <div class="menubox-input-l">
                        <div style="margin-bottom: 6px">留存限速</div>
                        <el-form-item prop="byte_ps">
                          <span style="position: relative">
                            <el-input
                              v-model="ruleForm.byte_ps"
                              placeholder="请输入"
                            >
                            </el-input>
                            <div class="unit">Mbps</div>
                          </span>
                        </el-form-item>
                      </div>
                      <div class="menubox-input-m">
                        <div style="margin-bottom: 6px">
                          留存总量
                          <el-tooltip
                            placement="top"
                            effect="light"
                            popper-class="sessionidTooltip"
                          >
                            <div slot="content">
                              全流量留存的模式下：不限制<br />规则留存的模式下：最高10GB"
                            </div>
                            <svg-icon icon-class="icon-hint" />
                          </el-tooltip>
                        </div>
                        <el-form-item prop="save_bytes">
                          <span style="position: relative">
                            <el-input
                              v-if="CurrentTask == 'ON'"
                              v-model="ruleForm.save_bytes"
                              placeholder="请输入"
                            >
                            </el-input>
                            <el-input v-else placeholder="请输入" value="10">
                            </el-input>
                            <div class="unit">GB</div>
                          </span>
                        </el-form-item>
                      </div>
                      <div class="menubox-input-r">
                        <div style="margin-bottom: 6px">已存总量</div>
                        <el-form-item prop="total_sum_bytes">
                          <span style="position: relative">
                            <el-input
                              v-model="ruleForm.rule_saved_num"
                              placeholder="请输入"
                            >
                            </el-input>
                            <div class="unit">B</div>
                          </span>
                        </el-form-item>
                      </div>
                    </div>
                  </div>
                </el-collapse-item>
                <el-collapse-item name="2" :disabled="collapse2">
                  <span slot="title" class="collapse-title"
                    >step2.添加规则集</span
                  >
                  <div class="addrulr">
                    <div class="tab-box">
                      <div class="tab-box-l">
                        <el-tabs v-model="activeName">
                          <el-tab-pane name="ip_rules">
                            <span slot="label"
                              >IP规则
                              <span
                                v-if="ruleData.id && ruleData.ip_rules !== null"
                                style="font-size: 10px; color: #ff9534"
                                >{{ ruleData.ip_rules.length }}</span
                              ></span
                            >
                            <IpRule
                              :ip-rule-data="ipRuleData"
                              @getIpRuleParam="getIpRuleParam"
                            />
                          </el-tab-pane>
                          <el-tab-pane name="pro_rules">
                            <span slot="label"
                              >协议规则<span
                                v-if="
                                  ruleData.id && ruleData.pro_rules !== null
                                "
                                style="
                                  font-size: 10px;
                                  color: #ff9534;
                                  margin-left: 2px;
                                "
                                >{{ ruleData.pro_rules.length }}</span
                              ></span
                            >
                            <Agreementrule
                              :pro-rule-data="proRuleData"
                              @getProRuleParam="getProRuleParam"
                            />
                          </el-tab-pane>
                          <el-tab-pane name="key_rules">
                            <span slot="label"
                              >特征规则<span
                                v-if="
                                  ruleData.id && ruleData.key_rules !== null
                                "
                                style="
                                  font-size: 10px;
                                  color: #ff9534;
                                  margin-left: 2px;
                                "
                                >{{ ruleData.key_rules.length }}</span
                              ></span
                            >
                            <Aspectrule
                              :key-rule-data="keyRuleData"
                              @getKeyRuleParam="getKeyRuleParam"
                            />
                          </el-tab-pane>
                          <el-tab-pane name="regex_rules">
                            <span slot="label"
                              >正则规则<span
                                v-if="
                                  ruleData.id && ruleData.regex_rules !== null
                                "
                                style="
                                  font-size: 10px;
                                  color: #ff9534;
                                  margin-left: 2px;
                                "
                                >{{ ruleData.regex_rules.length }}</span
                              ></span
                            >

                            <Regularrulr
                              :regex-rule-data="regexRuleData"
                              @getRegexRuleParam="getRegexRuleParam"
                            />
                          </el-tab-pane>
                          <el-tab-pane name="domain_rules">
                            <span slot="label"
                              >域名规则<span
                                v-if="
                                  ruleData.id && ruleData.domain_rules !== null
                                "
                                style="
                                  font-size: 10px;
                                  color: #ff9534;
                                  margin-left: 2px;
                                "
                                >{{ ruleData.domain_rules.length }}</span
                              ></span
                            >

                            <DomainRulr
                              :domain-rule-data="domainRuleData"
                              @getDomainRuleParam="getDomainRuleParam"
                            />
                          </el-tab-pane>
                        </el-tabs>
                      </div>
                    </div>
                  </div>
                </el-collapse-item>
                <el-collapse-item name="3" :disabled="collapse3">
                  <span slot="title" class="collapse-title"
                    >step3.程序响应</span
                  >
                  <div>
                    <div class="Program-box-top">
                      <div>
                        <el-radio v-model="respondType" :label="1">
                          动态库响应
                        </el-radio>
                        <el-radio v-model="respondType" :label="0">
                          复杂规则响应
                        </el-radio>
                      </div>
                      <div class="Program-box-top-dow">
                        <el-button style="font-size: 12px" @click="downloadTem">
                          动态库模板下载
                        </el-button>
                      </div>
                    </div>
                    <!-- 动态库响应 -->
                    <div v-show="respondType" class="Program-box">
                      <div class="Program-box-down">
                        <el-form-item
                          prop="lib_respond_lib"
                          :rules="
                            !!+ruleForm.lib_respond_open && respondType
                              ? rules.ruleEmpty
                              : rules.noRule
                          "
                        >
                          <div class="responseUp">
                            <el-upload
                              ref="upload"
                              class="left"
                              action
                              :http-request="uploadLibRuleFile"
                              :data="formData"
                              :show-file-list="false"
                              :on-change="handleSoFileChange"
                              :before-upload="RuleFile_BEFORE_UPLOAD"
                              accept=".so"
                            >
                              <div class="text">
                                响应文件:{{ ruleForm.lib_respond_lib
                                }}<span style="margin-left: 18px"
                                  >点击上传</span
                                >
                              </div>
                            </el-upload>
                            <div class="schedule">
                              <div>{{ responsepercent }}%</div>
                            </div>
                            <el-progress
                              :percentage="uploadPlanresponse"
                              :format="responseformat"
                            ></el-progress>
                          </div>
                        </el-form-item>
                        <el-form-item prop="lib_respond_config">
                          <div class="configUp">
                            <el-upload
                              ref="uploadconfig"
                              class="left"
                              action
                              :http-request="uploadLibRuleConfigFile"
                              :data="formData"
                              :show-file-list="false"
                              :on-change="handleConfigFileChange"
                              :before-upload="Config_BEFORE_UPLOAD"
                              accept=".conf,.txt,.json,.xml"
                            >
                              <div class="text">
                                配置文件:{{ ruleForm.lib_respond_config
                                }}<span style="margin-left: 18px"
                                  >点击上传</span
                                >
                              </div>
                            </el-upload>
                            <div class="schedule">
                              <div>{{ configpercent }}%</div>
                            </div>
                            <el-progress
                              :percentage="uploadPlanconfig"
                              :format="configformat"
                            ></el-progress>
                          </div>
                        </el-form-item>
                      </div>
                    </div>
                    <Program
                      v-show="!respondType"
                      ref="ruleRespond"
                      :rule-respond-data="ruleRespondData"
                      @getRuleRespondParam="getRuleRespondParam"
                    />
                  </div>
                </el-collapse-item>
              </el-collapse>
              <div class="btn">
                <el-button @click="closedrawer">取消</el-button>
                <el-button
                  v-if="showbtn"
                  style="color: #fff"
                  type="success"
                  @click="openresponse"
                >
                  程序响应
                </el-button>
                <el-button
                  v-if="!showbtn"
                  style="width: 170px; color: #fff"
                  type="danger"
                  @click="closeresponse"
                >
                  关闭程序响应
                </el-button>
                <el-button style="color: #fff" type="primary" @click="saveRules"
                  >确认</el-button
                >
              </div>
            </el-form>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { AddRulr, EditRules } from "@/api/AddRulrs/AddRulr";
import api from "@/api/offline";
import {
  validateIP,
  validateIPPro,
  formatNumber,
  validateRuleName,
} from "@/utils";
import Program from "../Program.vue";
import IpRule from "./IpRule.vue";
import Agreementrule from "./AgreementRule.vue";
import Aspectrule from "./AspectRule.vue";
import Regularrulr from "./RegularRulr.vue";
import DomainRulr from "./DomainRulr.vue";
import axios from "axios";
import FileSaver from "file-saver";
import { getTags } from "@/api/Conversational/conversational";
import mixins from "@/views/offline/mixins";
export default {
  name: "AddRulr",
  components: {
    IpRule,
    Program,
    Agreementrule,
    Aspectrule,
    Regularrulr,
    DomainRulr,
  },
  mixins: [mixins],
  props: {
    dialog: false,
    ruleData: {},
  },
  data() {
    return {
      loading: false,
      options: [], //采集模式数据
      activemenu: false, //控制高级菜单按钮
      list: [],
      ruleForm: {},
      sub_rule: {}, //存放规则数据
      ipRuleData: [], //ip规则数据
      proRuleData: [], //协议规则数据
      keyRuleData: [],
      regexRuleData: [],
      domainRuleData: [],
      ruleRespondData: {},
      rules: {
        rule_name: [
          { required: true, message: "规则名称不能为空" },
          {
            required: true,
            message: "仅支持英文、数字和下划线",
            trigger: ["change"],
            validator: validateRuleName,
          },
          {
            min: 1,
            max: 15,
            message: "长度在15字符",
            trigger: "blur",
            equired: true,
          },
        ],
        capture_mode: [{ required: true, message: "不能为空" }],
        rule_level: [{ required: true, message: "不能为空" }],
        ruleIp: [
          { required: true, message: "不能为空", trigger: ["blur"] },
          {
            required: true,
            message: "请输入合法IP地址",
            trigger: ["change"],
            validator: validateIP,
          },
        ],
        ruleIPPro: [
          { required: true, message: "不能为空", trigger: ["blur"] },
          {
            required: true,
            message: "输入格式不正确",
            trigger: ["change"],
            validator: validateIPPro,
          },
        ],
      },
      unrestrict: true, //规则留存默认不限制
      checked: false,
      timer: null,
      step: "1",
      activeNames: "1",
      respondType: 1,
      activeName: "ip_rules",
      //上传数据
      formData: {},
      RuleFile: {},
      ConfigFile: {},
      files: [],
      value: "",
      collapse1: false,
      collapse2: false,
      collapse3: true,
      flag: false,
      showbtn: true,
      // 上传相关字段
      uploadPlanresponse: 0,
      uploadPlanconfig: 0,
      // 百分比数
      responsepercent: 0,
      configpercent: 0,
    };
  },
  computed: {
    // 从vuex保存的主从任务信息做处理，判断总量留存   ON-不限制 OFF-10G
    CurrentTask() {
      let Taskarr = this.$store.state.conversational.CurrentTask;
      let brr = Taskarr.filter((item) => {
        if (item.task_id + "" == localStorage.getItem("task_id")) {
          return item.fullflow_state;
        }
      });
      return brr[0].fullflow_state;
    },
    capturemode() {
      let newarr = [];
      for (var key in this.$store.state.long.Dict.capture_mode) {
        let tempMode = {};
        tempMode.label = this.$store.state.long.Dict.capture_mode[key];
        tempMode.value = parseInt(key);
        newarr.push(tempMode);
      }
      return newarr;
    },
  },
  watch: {
    ruleForm: {
      handler(val) {
        if (val.lib_respond_open == 1) {
          if (!this.hasRules()) {
            this.$message.error("启用失败，未配置任何规则。");
            this.ruleForm.lib_respond_open = "0";
          }
        }
      },
      deep: true,
      immediate: true,
    },
    capturemode: {
      handler(vla) {},
    },
  },
  methods: {
    async initStandData() {
      this.ruleForm = {
        // 基础信息
        id: "",
        rule_name: "",
        capture_mode: "",
        rule_level: "45",
        rule_desc: "",
        pcap_drop: "0", //
        pb_drop: "0",
        lib_respond_open: "0",
        lib_data_conf: "",
        lib_data_so: "",
        // rule_saved: 0,
        byte_ps: "",
        save_bytes: "",
        rule_saved_num: "",
        lib_respond_lib: "",
        lib_respond_config: "",
        lib_respond_session_end: "0",
        lib_respond_pkt_num: "", //TODO
        // task_id: localStorage.getItem("task_id"),
        batch_id: 1000001,
        /* 留存总量 */
        buffer_capacity: "",
      };
      this.step = "1";
      this.respondType = 1;
      // TODO
      this.collapse3 = true;
      this.showbtn = true;
      this.activeNames = "1";
      this.files = [];
      this.ipRuleData = [];
      this.proRuleData = [];
      this.keyRuleData = [];
      this.regexRuleData = [];
      this.domainRuleData = [];
      this.ruleRespondData = [];
      this.activeName = "ip_rules";
      this.ruleForm.byte_ps = "";
      this.ruleForm.save_bytes = "";
      this.ruleForm.rule_saved_num = "";
      this.ruleForm.lib_respond_session_end = 0;
      this.ruleForm.lib_respond_pkt_num = 100000;
    },
    // 将后端返回数据规范为前端可展示绑定数据
    async changeShowFormat() {
      await this.initStandData();
      if (this.ruleData.hasOwnProperty("id")) {
        this.ruleForm.id = this.ruleData.id;
        this.ruleForm.rule_id = this.ruleData.rule_id;
        this.ruleForm.lib_respond_open = "" + this.ruleData.lib_respond_open;
        this.ruleForm.lib_data_conf = this.ruleData.lib_data_conf;
        this.ruleForm.lib_data_so = this.ruleData.lib_data_so;
        if (this.ruleData.lib_respond_open) {
          this.ruleForm.lib_respond_lib = this.ruleData.lib_respond_lib;
          this.ruleForm.lib_respond_config = this.ruleData.lib_respond_config;
          this.ruleForm.lib_respond_pkt_num =
            this.ruleData.lib_respond_pkt_num || "";
        }
        this.ruleForm.pcap_drop = "" + this.ruleData.pcap_drop;
        this.ruleForm.pb_drop = "" + this.ruleData.pb_drop;
        this.ruleForm.byte_ps = this.ruleData.byte_ps;
        this.ruleForm.rule_name = this.ruleData.rule_name;
        console.log(this.ruleData);
        this.ruleForm.capture_mode = this.ruleData.capture_mode;
        this.ruleForm.save_bytes = this.ruleData.save_bytes;
        this.ruleForm.rule_saved_num = this.ruleData.rule_saved_num;
        this.ruleForm.rule_desc = this.ruleData.rule_desc;
        if (0 <= this.ruleData.rule_level && this.ruleData.rule_level <= 30) {
          this.ruleForm.rule_level = "15";
        } else if (
          31 <= this.ruleData.rule_level &&
          this.ruleData.rule_level <= 60
        ) {
          this.ruleForm.rule_level = "45";
        } else if (
          61 <= this.ruleData.rule_level &&
          this.ruleData.rule_level <= 80
        ) {
          this.ruleForm.rule_level = "70";
        } else if (
          81 <= this.ruleData.rule_level &&
          this.ruleData.rule_level <= 90
        ) {
          this.ruleForm.rule_level = "85";
        } else if (
          91 <= this.ruleData.rule_level &&
          this.ruleData.rule_level <= 100
        ) {
          this.ruleForm.rule_level = "95";
        }
        if (this.ruleData.ip_rules !== null) {
          this.activeName = "ip_rules";
        } else if (this.ruleData.pro_rules !== null) {
          this.activeName = "pro_rules";
        } else if (this.ruleData.key_rules !== null) {
          this.activeName = "key_rules";
        } else if (this.ruleData.regex_rules !== null) {
          this.activeName = "regex_rules";
        } else if (this.ruleData.domain_rules !== null) {
          this.activeName = "domain_rules";
        } else {
          this.activeName = "ip_rules";
        }
        if (this.ruleData.ip_rules !== null) {
          this.ipRuleData = this.ruleData.ip_rules;
        }
        if (this.ruleData.pro_rules !== null) {
          this.proRuleData = this.ruleData.pro_rules;
        }
        if (this.ruleData.key_rules !== null) {
          this.keyRuleData = this.ruleData.key_rules;
        }
        if (this.ruleData.regex_rules !== null) {
          this.regexRuleData = this.ruleData.regex_rules;
        }
        if (this.ruleData.domain_rules !== null) {
          this.domainRuleData = this.ruleData.domain_rules;
        }
        // TODO
        if (this.ruleData.detail_respond !== null) {
          this.ruleRespondData = this.ruleData.detail_respond;
          if (this.ruleRespondData.EXPR) {
            this.ruleRespondData.EXPR = this.ruleRespondData.EXPR.slice(
              1,
              this.ruleRespondData.EXPR.length - 1
            );
          }
          if (Object.keys(this.ruleRespondData).length) {
            this.respondType = 0;
          }
        }
      }
    },
    // 检验当前规则
    hasRules() {
      this.sub_rule = {};
      // ip规则
      if (this.ipRuleData.length !== 0) {
        this.sub_rule.ip_rules = this.ipRuleData;
      }
      // 协议规则
      if (this.proRuleData.length !== 0) {
        this.sub_rule.pro_rules = this.proRuleData;
      }
      // 特征规则
      if (this.keyRuleData.length !== 0) {
        this.sub_rule.key_rules = this.keyRuleData;
      }
      // 正则规则
      if (this.regexRuleData.length !== 0) {
        this.sub_rule.regex_rules = this.regexRuleData;
      }
      // 域名规则
      if (this.domainRuleData.length !== 0) {
        this.sub_rule.domain_rules = this.domainRuleData;
      }
      var arr = Object.keys(this.sub_rule);
      return arr.length;
    },
    // 提交表单
    saveRules() {
      this.$refs["ruleForm"].validate(async (valid) => {
        let filterValid = true;
        // 检验是否打开程序响应
        if (this.ruleForm.lib_respond_open) {
          if (this.ruleForm.pcap_drop && !this.respondType) {
            if (this.$refs.ruleRespond.ruleRespondList.length === 0) {
              this.$message.error("检验错误，未配置复杂规则。");
              this.loading = false;
              return false;
            }
            filterValid = this.$refs.ruleRespond.ValidationRules();
          }
        }
        if (valid && filterValid) {
          await this.submitData();
          // await this.getTagsfn();
          this.loading = true;
        } else {
          this.$message.error("检验错误，请检查必填项是否填写。");
          this.loading = false;
          return false;
        }
      });
    },
    getTagsfn() {
      getTags()
        .then((res) => {
          if (res.err == 0) {
            this.$store.commit("conversational/tagseachlist", res.data);
          } else {
            console.log("err:" + res.err + ",msg:" + res.msg);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    submitData() {
      if (!this.hasRules()) {
        this.$message.error("操作失败，未配置任何规则。");
        setTimeout(() => {
          this.loading = false;
        }, 600);
        return;
      }
      this.capturemode.forEach((item) => {
        if (this.ruleForm.capture_mode === item.label) {
          this.ruleForm.capture_mode = item.value;
        }
      });
      if (this.flag) return;
      let param = { ...this.ruleForm };
      param.lib_respond_open = +param.lib_respond_open; //程序响应
      param.pcap_drop = +param.pcap_drop; //流量响应
      param.pb_drop = +param.pb_drop; //日志响应
      param.byte_ps = +param.byte_ps; //流量现存
      param.save_bytes = param.save_bytes ? param.save_bytes : -1; //留存总量
      param.rule_saved_num = +param.rule_saved_num; //已存总量
      param.lib_respond_session_end = +param.lib_respond_session_end || 0;
      if (!param.lib_respond_open) {
        delete param.lib_respond_lib;
        delete param.lib_respond_config;
        delete param.lib_respond_session_end;
        delete param.lib_respond_pkt_num;
      } else {
        param.detail_respond = JSON.parse(JSON.stringify(this.ruleRespondData));
        param.detail_respond["EXPR"] =
          "(" + this.$refs.ruleRespond.filter.operation + ")";
        if (this.respondType) {
          param.detail_respond = {};
          if (param.lib_respond_session_end) {
            param.lib_respond_pkt_num = 0 || 100000;
          } else {
            param.lib_respond_pkt_num = +param.lib_respond_pkt_num || 100000;
          }
        } else {
          param.lib_respond_lib = new Date().getTime() + ".so";
          delete param.lib_respond_session_end;
          param.lib_respond_pkt_num = 1 || 100000;
        }
      }
      if (this.isOffLine) {
        if (this.offlineId) {
          param.task_id = this.offlineId;
        }
      } else {
        param.task_id = localStorage.getItem("task_id");
      }
      Object.assign(param, this.sub_rule);
      let formdata = new FormData();
      formdata.append("data", JSON.stringify(param));
      for (let i = 0; i < this.files.length; i++) {
        formdata.append("files", this.files[i]);
      }
      if (param.id) {
        if (this.isOffLine) {
          if (this.offlineId) {
            api.offlineFeatureUpdate(formdata).then((res) => {
              if (res == undefined) {
                this.loading = false;
              }
              this.flag = false;
              if (res.err === 0) {
                this.loading = false;
                this.$message({
                  type: "success",
                  message: "修改成功!",
                });
                this.files = [];
                this.$emit("closeAddDrawer", "refresh");
              } else {
                this.loading = false;
                this.$message.error(`${res.msg}`);
              }
            });
          }
        } else {
          EditRules(formdata).then((res) => {
            if (res == undefined) {
              this.loading = false;
            }
            this.flag = false;
            if (res.err === 0) {
              this.loading = false;
              this.$message({
                type: "success",
                message: "修改成功!",
              });
              this.files = [];
              this.$emit("closeAddDrawer", "refresh");
            } else {
              this.loading = false;
              this.$message.error(`${res.msg}`);
            }
          });
        }
      } else {
        if (this.isOffLine) {
          if (this.offlineId) {
            api
              .offlineFeatureAdd(formdata)
              .then((res) => {
                this.flag = false;
                this.loading = false;
                if (res.err == 0) {
                  this.dialog = false;
                  this.$message({
                    type: "success",
                    message: "添加成功!",
                  });
                  this.$emit("closeAddDrawer", "refresh");
                }
              })
              .catch((res) => {
                this.loading = false;
              });
          }
        } else {
          AddRulr(formdata)
            .then((res) => {
              this.flag = false;
              this.loading = false;
              if (res.err == 0) {
                this.dialog = false;
                this.$message({
                  type: "success",
                  message: "添加成功!",
                });
                this.$emit("closeAddDrawer", "refresh");
              }
            })
            .catch((res) => {
              this.loading = false;
            });
        }
      }
    },
    closedrawer() {
      this.$emit("closedrawerdate", false);
    },
    handleChange(val) {
      if (val[0]) {
        this.step = val[0];
      }
    },
    showmenu() {
      this.activemenu = !this.activemenu;
    },
    // 打开程序响应
    openresponse() {
      if (!this.hasRules()) {
        this.$message.error("启用失败，未配置任何规则。");
        this.collapse3 = true;
      } else {
        this.collapse3 = false;
        this.showbtn = false;
        this.ruleForm.lib_respond_open = 1;
        this.activeNames = "3";
        this.step = "3";
        this.responsepercent = 0;
        this.uploadPlanresponse = 0;
        this.uploadPlanconfig = 0;
      }
    },
    // 关闭程序响应
    closeresponse() {
      this.collapse3 = true;
      this.activeNames = "2";
      this.step = "2";
      this.showbtn = true;
      this.ruleForm.lib_respond_open = 0;
    },
    opencollapse(id) {
      if (id == 1) {
        this.activeNames = "1";
        this.step = "1";
      }
      if (id == 2) {
        this.activeNames = "2";
        this.step = "2";
      }
      if (id == 3) {
        if (!this.hasRules()) {
          this.$message.error("启用失败，未配置任何规则。");
          this.collapse3 = true;
        } else {
          this.collapse3 = false;
          this.showbtn = false;
          this.activeNames = "3";
          this.step = "3";
          this.ruleForm.lib_respond_open = 1;
        }
      }
    },
    responseformat(percentage) {
      this.responsepercent = percentage;
      return (percentage = " ");
    },
    configformat(percentage) {
      this.configpercent = percentage;
      return (percentage = " ");
    },
    // TODO 上传文件
    uploadLibRuleFile(param) {
      this.files.push(param.file);
    },
    // 上传文件前的回调
    RuleFile_BEFORE_UPLOAD(file) {
      this.uploadPlanresponse = 50;
      console.log(file);
      let index = file.name.indexOf(".");
      let result = file.name.substr(index + 1, file.name.length);
      if (result === "so") {
        setTimeout(() => {
          this.uploadPlanresponse = 100;
        }, 1000);
      } else {
        this.$message.error("上传文件错误！");
      }
    },
    // 上传文件前的回调
    Config_BEFORE_UPLOAD(file) {
      this.uploadPlanconfig = 50;
      let index = file.name.indexOf(".");
      let result = file.name.substr(index + 1, file.name.length);
      if (
        result == "conf" ||
        result == "txt" ||
        result == "xml" ||
        result == "json"
      ) {
        setTimeout(() => {
          this.uploadPlanconfig = 100;
        }, 1000);
      } else {
        this.$message.error("上传文件错误！");
        this.uploadPlanconfig = 0;
      }
    },
    handleSoFileChange(file, fileList) {
      if (fileList.length > 1) {
        fileList.splice(0, 1);
      }
    },
    // 配置文件
    uploadLibRuleConfigFile(param) {
      this.files.push(param.file);
    },
    handleConfigFileChange(file, fileList) {
      if (fileList.length > 1) {
        fileList.splice(0, 1);
      }
    },
    getIpRuleParam(val) {
      this.ipRuleData = val;
    },
    getProRuleParam(val) {
      this.proRuleData = val;
    },
    getKeyRuleParam(val) {
      this.keyRuleData = val;
    },
    getRegexRuleParam(val) {
      this.regexRuleData = val;
    },
    getDomainRuleParam(val) {
      this.domainRuleData = val;
    },
    getRuleRespondParam(val) {
      this.ruleRespondData = val;
    },
    // 上传弹窗点击取消
    UPLOAD_CANCEL_response() {
      if (this.uploadPlanresponse > 0 && this.uploadPlanresponse <= 100) {
        this.$refs.upload.abort();
        this.$refs.upload.clearFiles();
        this.RuleFile = "";
        this.uploadPlanresponse = 0;
        this.$message.warning("已取消上传！");
      } else {
        return;
      }
    },
    UPLOAD_CANCEL_config() {
      if (this.uploadPlanconfig > 0 && this.uploadPlanconfig <= 100) {
        this.$refs.uploadconfig.abort();
        this.$refs.uploadconfig.clearFiles();
        this.ConfigFile = "";
        this.uploadPlanconfig = 0;
        this.$message.warning("已取消上传！");
      } else {
        return;
      }
    },
    // 下载动态库模板
    downloadTem() {
      let baseURL;
      if (this.isOffLine) {
        if (process.env.VUE_APP_BASE_API === "") {
          baseURL = `${process.env.VUE_APP_BASE_API}/api/offline/feature/template/zip`;
        } else {
          baseURL =
            process.env.VUE_APP_BASE_API + "/offline/feature/template/zip";
        }
      } else {
        if (process.env.VUE_APP_BASE_API === "") {
          baseURL = `${process.env.VUE_APP_BASE_API}/api/feature/template/zip`;
        } else {
          baseURL = process.env.VUE_APP_BASE_API + "/feature/template/zip";
        }
      }

      axios({
        method: "GET",
        url: baseURL, // 后端下载接口地址
        responseType: "blob", // 设置接受的流格式
        headers: {
          token: `${getToken()}`,
        },
      }).then((res) => {
        if (res.err === 40005) {
          this.$message.error(res.msg);
        } else {
          const blob = new Blob([res.data], { type: "application/json" });
          FileSaver.saveAs(blob, res.headers["content-disposition"]);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep {
  .el-drawer {
    width: 871px !important;
  }
}

.drawer-box {
  display: flex;
  position: relative;
  &-top {
    display: flex;
    justify-content: space-between;
    padding: 12px 16px;
    &-title {
      font-family: "Alibaba PuHuiTi";
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      color: #0f0f13;
      display: flex;
      align-items: center;
    }
    &-left {
      i {
        width: 9px;
        height: 9px;
        margin-right: 18px;
        font-weight: bolder;
        cursor: pointer;
      }
    }
  }
  &-down {
    display: flex;
    justify-content: space-between;
    padding: 0px 16px;
    margin-bottom: 100px;
    // 左步骤图
    &-l {
      width: 171px;
      height: 135px;
      display: flex;
      position: relative;
      margin-top: 34px;
      .active {
        color: #ffffff;
        background: #116ef9;
        font-size: 8px;
      }
      .default {
        background: #cecece;
        font-size: 8px;
      }
      .textdefault {
        color: #116ef9;
        font-size: 8px;
      }
      .step {
        margin: 0 30px;
        height: 105px;
        width: 1px;
        border-right: 4px dotted #cecece;
        &-box {
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          position: absolute;
          top: -20px;
          left: -1.6px;
          .Dot1 {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .activeicon {
              color: #116ef9;
            }
            .icon1 {
              width: 27px;
              height: 19px;
            }
            div:nth-child(1) {
              position: relative;
              width: 27px;
              height: 19px;
              span {
                position: absolute;
                top: 3px;
                left: 8px;
              }
            }
            div:nth-last-child(1) {
              flex: 1;
              display: flex;
              justify-content: flex-start;
              align-items: center;
            }
          }
          .Dot2 {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .icon2 {
              width: 27px;
              height: 19px;
            }
            div:nth-child(1) {
              width: 27px;
              height: 19px;
              position: relative;
              span {
                position: absolute;
                top: 3px;
                left: 8px;
              }
            }
            div:nth-last-child(1) {
              flex: 1;
              display: flex;
              justify-content: flex-start;
              align-items: center;
            }
          }
          .Dot3 {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .icon3 {
              width: 27px;
              height: 19px;
            }
            div:nth-child(1) {
              width: 27px;
              height: 19px;
              position: relative;
              span {
                position: absolute;
                top: 3px;
                left: 8px;
              }
            }
            div:nth-last-child(1) {
              flex: 1;
              display: flex;
              justify-content: flex-start;
              align-items: center;
            }
          }
        }
      }
    }
    // 右表单
    &-r {
      width: 676px;
      // flex: 1;
      ::v-deep .el-collapse-item__content {
        padding: 0;
      }
      .el-form-item {
        margin: 0;
      }
      // 基础信息
      .BasicIfo {
        ::v-deep {
          .el-form-item__label {
            font-style: normal;
            font-weight: 500;
            font-size: 12px;
            line-height: 16px;
            text-transform: capitalize;
            color: #0f0f13;
          }
        }
        &-top {
          width: 90%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          ::v-deep .el-input__inner {
            height: 27px !important;
            font-weight: 400;
            font-size: 12px;
            padding-left: 5px;
          }
          ::v-deep .el-input {
            width: 180px;
          }
          ::v-deep .el-input__suffix {
            height: 54px;
            top: -8px;
          }
          div:nth-child(3) {
            div:nth-child(2) {
              .el-radio {
                margin: 0;
              }
              ::v-deep .el-radio__label {
                padding: 0 10px;
              }
            }
          }
          .rulelevel {
            ::v-deep {
              .el-radio__inner {
                border: 1.5px solid #767684;
              }
              .el-radio__input.is-checked .el-radio__inner {
                border-color: #409eff;
                background: #ffffff;
              }
              .el-radio__inner::after {
                width: 8px;
                height: 8px;
                background: #116ef9;
              }
            }
          }
        }
        &-center {
          margin-top: 20px;
          &-radioStyle {
            display: block;
            width: 100%;
            margin-top: 25px;
          }
        }
        &-down {
          margin-top: 20px;
          div:nth-child(2) {
            ::v-deep .el-input__inner {
              height: 27px !important;
              font-weight: 400;
              font-size: 12px;
              padding-left: 5px;
            }
          }
        }
      }
      .advancedmenu {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 15px;
        margin-bottom: 14px;
        width: 100%;
        &-box {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 1.6rem;
          .active {
            color: #116ef9;
          }
          span {
            text-align: center;
            color: #767684;
            font-weight: 400;
            font-size: 10px;
            line-height: 14px;
          }
        }
      }
      .activestyle {
        color: #116ef9 !important;
      }
      .advancedmenu::after {
        content: "";
        width: 100%;
        border-bottom: 1px dashed #f2f3f7;
      }
      .advancedmenu::before {
        content: "";
        width: 100%;
        border-bottom: 1px dashed #f2f3f7;
      }
      .menubox {
        .unit {
          position: absolute;
          top: -10px;
          right: 10px;
          font-size: 14px;
          // color: #cecece;
          z-index: 3;
        }
        &-radio {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          &-flow {
            margin-right: 80px;
            &-title {
              font-weight: 500;
              font-size: 12px;
              text-transform: capitalize;
              // margin-bottom: 8px;
            }
          }
          &-log {
            &-title {
              font-weight: 500;
              font-size: 12px;
              text-transform: capitalize;
              // margin-bottom: 8px;
            }
          }
        }
        &-input {
          margin-top: 10px;
          display: flex;
          justify-content: flex-start;
          align-items: centers;
          &-l {
            margin-right: 24px;
          }
          &-m {
            margin-right: 24px;
          }
          ::v-deep .el-input__inner {
            height: 27px !important;
            // background: #f2f3f7 !important;
            font-weight: 400;
            font-size: 12px;
            padding: 0 10px;
          }
        }
      }
      // 添加规则
      .addrulr {
        width: 100%;
        height: 100%;
        border: 1px solid #f2f3f7;
        box-sizing: border-box;
        border-radius: 8px;
        position: relative;
        // 顶部tap栏
        .tab-box {
          width: 100%;
          background: #ffffff;
          border-radius: 8px;
          padding: 12px 16px;
          box-sizing: border-box;
          &-r {
            .el-button {
              width: 35px;
              height: 27px;
              display: flex;
              justify-content: center;
              align-items: center;
              position: absolute;
              top: 14px;
              right: 20px;
            }
          }
          ::v-deep .el-tabs__header {
            width: 460px;
          }
          ::v-deep .el-tabs__nav-scroll {
            height: 26px;
            font-size: 14px;
            position: relative;
            .el-tabs__nav {
              height: 100%;
            }
            .el-tabs__item {
              height: auto;
              line-height: 1;
            }
            .el-tabs__item.is-active {
              font-weight: 700;
            }
          }
        }
      }
      // 程序响应
      .Program-box-top {
        margin-bottom: 10px;
        ::v-deep {
          .el-radio__inner {
            border: 1.5px solid #767684;
          }
          .el-radio__input.is-checked .el-radio__inner {
            border-color: #409eff;
            background: #ffffff;
          }
          .el-radio__inner::after {
            width: 8px;
            height: 8px;
            background: #116ef9;
          }
        }
        display: flex;
        justify-content: space-between;
        align-items: center;
        &-dow {
          font-weight: 500;
          font-size: 14px;
          margin-right: 48px;
          ::v-deep {
            .el-button {
              padding: 8px 8px;
            }
          }
        }
      }
      .Program-box-down {
        display: flex;
        justify-content: flex-start;
        .text {
          font-weight: 500;
          font-size: 14px;
          span {
            color: #116ef9;
          }
        }
        ::v-deep {
          .el-form-item__content {
            line-height: 25px;
          }
        }
        .responseUp {
          width: 300px;
          height: 76px;
          border: 1px solid #f2f3f7;
          box-sizing: border-box;
          border-radius: 8px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          margin-right: 20px;
          padding: 10px;
          .schedule {
            display: flex;
            justify-content: space-between;
          }
          ::v-deep {
            .el-progress-bar {
              padding: 0;
            }
          }
        }
        .configUp {
          width: 300px;
          height: 76px;
          border: 1px solid #f2f3f7;
          box-sizing: border-box;
          border-radius: 8px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          margin-right: 20px;
          padding: 10px;
          .schedule {
            display: flex;
            justify-content: space-between;
          }
          ::v-deep {
            .el-progress-bar {
              padding: 0;
            }
          }
        }
      }
      ::v-deep .el-collapse-item__header {
        border: none;
      }
      .collapse-title {
        flex: 1 0 90%; //位于左侧
        order: 1;
      }
      ::v-deep .el-collapse {
        border: none;
      }
      ::v-deep .el-collapse-item__wrap {
        border: none;
      }
    }
  }
  .btn {
    z-index: 799;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 16px;
    width: 100%;
    height: 64px;
    background: #ffffff;
    position: absolute;
    bottom: 0;
    right: 0;
    .el-button {
      width: 78px;
      height: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>

/* 协议规则 */
<template>
  <div class="agreement-box p-relative">
    <el-form ref="proRule" :rules="rules" :model="formData">
      <div class="agreement-box-top">
        <div class="title">协议名称</div>
        <el-form-item prop="pro_id" :rules="rules.ruleEmpty">
          <el-select
            v-model="formData.pro_id"
            placeholder="请选择"
            value-key="value"
            filterable
            :filter-method="handleIDProFilter"
            @visible-change="handleIDPrpVisibleChange"
          >
            <el-option
              v-for="item in id_pro_options"
              :key="item.id"
              :label="item.protocol_value"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </div>
      <div class="agreement-box-mid">
        <div class="title">
          端口范围
          <span style="color: #3c8fff"
            >注：点击<svg-icon icon-class="linkicon" />激活对应端口范围</span
          >
        </div>
        <el-form-item>
          <div class="agreement-box-mid-box">
            <div class="agreement-box-mid-box-input_1">
              <el-input v-model="startPort" maxlength="1" disabled></el-input>
            </div>
            <div @click="lockPort(1)">
              <svg-icon
                icon-class="linkicon"
                :class="formData.port_rule.property1 ? 'active' : ''"
              />
            </div>

            <div class="agreement-box-mid-box-input_2">
              <el-input
                v-model="formData.port_rule.low_port"
                maxlength="6"
              ></el-input>
            </div>

            <div @click="lockPort(2)">
              <svg-icon
                icon-class="linkicon"
                :class="formData.port_rule.property2 ? 'active' : ''"
              />
            </div>

            <div class="agreement-box-mid-box-input_3">
              <el-input
                v-model="formData.port_rule.high_port"
                maxlength="7"
              ></el-input>
            </div>

            <div @click="lockPort(3)">
              <svg-icon
                icon-class="linkicon"
                :class="formData.port_rule.property3 ? 'active' : ''"
              />
            </div>

            <div class="agreement-box-mid-box-input_4">
              <el-input v-model="endPort" maxlength="7" disabled></el-input>
            </div>
            <div class="agreement-box-mid-box-radio">
              <el-checkbox-group v-model="formData.port_rule.portType">
                <el-checkbox label="客户端端口"></el-checkbox>
                <el-checkbox label="服务器端口"></el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </el-form-item>
      </div>
    </el-form>
    <div class="agreement-box-down">
      <el-table ref="proRuleList" :data="proRuleList" style="width: 100%">
        <el-table-column type="index" label="序号" fixed> </el-table-column>
        <el-table-column prop="pro_id" label="协议ID"> </el-table-column>
        <el-table-column prop="ProName" label="协议名称"> </el-table-column>
        <el-table-column prop="portRange" label="端口范围"> </el-table-column>
        <el-table-column prop="port_rule.portType" label="端口类型 ">
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <div class="btn">
              <el-button
                type="text"
                size="mini"
                @click="deleteRuleInfo(scope.row)"
              >
                删除
              </el-button>
              <el-button
                size="mini"
                type="text"
                @click="editRuleInfo(scope.row)"
              >
                修改
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <tablescroll :table-ref="$refs.proRuleList"></tablescroll>
    </div>
    <!-- 添加\保存 按钮 -->
    <div class="tab-box-btn">
      <div v-if="showsave" class="tab-box-btn-l" @click="addToList('ipRule')">
        <el-button>
          <svg-icon icon-class="frule-add" />
        </el-button>
      </div>
      <div v-if="!showsave" class="tab-box-btn-r" @click="editProRule">
        <el-button>
          <svg-icon icon-class="saveRule" />
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import tablescroll from "../../../../components/TableScroll/idnex.vue";
import { validatePort } from "@/utils";

export default {
  name: "Agreement",
  components: {
    tablescroll,
  },
  props: ["proRuleData"],
  data() {
    return {
      showsave: true,
      formData: {
        pro_id: "",
        port_rule: {
          property1: true,
          property2: false,
          property3: false,
          low_port: 10000,
          high_port: 49151,
          portType: ["服务器端口", "客户端端口"],
        },
      },
      proRuleList: [],
      proRuleParam: [],
      id_pro_options: [],
      id_pro_full_options: [],
      showAddForm: this.showForm,
      startPort: 0,
      endPort: 65535,
      proType: { 1: "连接", 2: "单包", 3: "负载" },
      rules: {
        ProID: [{ required: true, message: "不能为空", trigger: [] }],
        ruleEmpty: [{ required: true, message: "不能为空", trigger: [] }],
        rulePort: [
          {
            required: false,
            message: "请输入正确的端口范围",
            trigger: ["change"],
            validator: validatePort,
          },
        ],
      },
    };
  },
  computed: {
    dict() {
      return this.$store.state.long.Dict;
    },
  },
  watch: {
    proRuleData: {
      handler() {
        this.$nextTick(() => {
          this.$refs.proRule.clearValidate();
        });
        this.changeShowFormat();
      },
      deep: true,
    },
  },
  mounted() {
    this.initIDProOptions();
    this.proRuleList = [];
    this.proRuleParam = [];
  },
  methods: {
    initFormData() {
      this.formData = {
        pro_id: "",
        port_rule: {
          property1: true,
          property2: false,
          property3: false,
          low_port: 10000,
          high_port: 49151,
          portType: ["服务器端口", "客户端端口"],
        },
      };
      this.showsave = true;
    },
    // 展示数据转换为规则传参数据
    showDataToRuleParams() {
      this.proRuleParam = [];
      this.proRuleList.forEach((item) => {
        let tempProRule = {
          pro_id: item.pro_id,
        };
        let tempPortRule = {};
        tempPortRule.property = this.propertyToValue(
          item.port_rule.property1,
          item.port_rule.property2,
          item.port_rule.property3
        );
        tempPortRule.low_port = item.port_rule.low_port;
        tempPortRule.high_port = item.port_rule.high_port;
        if (item.port_rule.portType.length > 1) {
          tempPortRule.sign = 3;
        } else {
          if (item.port_rule.portType.length == 0) tempPortRule.sign = 0;
          else {
            if (item.port_rule.portType.includes("客户端端口"))
              tempPortRule.sign = 1;
            else tempPortRule.sign = 2;
          }
        }
        tempProRule.port_rule = tempPortRule;
        this.proRuleParam.push(tempProRule);
      });
      this.$emit("getProRuleParam", this.proRuleParam);
    },
    // 将后端返回数据规范为前端可展示绑定数据
    changeShowFormat() {
      this.initFormData();
      this.proRuleList = [];
      this.proRuleParam = [];
      // this.showAddForm = true
      if (this.proRuleData.length !== 0) {
        for (let proRuleItem of this.proRuleData) {
          console.log(proRuleItem, "协议id");
          let tempItem = {
            index: Math.floor(Math.random() * 10000),
            pro_id: proRuleItem.pro_id,
            port_rule: {},
          };
          this.id_pro_options.forEach((item) => {
            if (item.id === proRuleItem.pro_id) {
              tempItem.ProName = item.protocol_value;
            }
          });
          tempItem.port_rule.low_port = proRuleItem.port_rule.low_port;
          tempItem.port_rule.high_port = proRuleItem.port_rule.high_port;
          tempItem.port_rule.property = proRuleItem.port_rule.property;
          tempItem.port_rule.property3 = !!parseInt(
            tempItem.port_rule.property / 4
          );
          tempItem.port_rule.property2 = !!parseInt(
            (tempItem.port_rule.property % 4) / 2
          );
          tempItem.port_rule.property1 = !!(
            (tempItem.port_rule.property % 4) %
            2
          );
          let portRange = [];
          if (
            tempItem.port_rule.property1 &&
            tempItem.port_rule.low_port != null
          ) {
            if (tempItem.port_rule.low_port) {
              portRange.push(`0~${tempItem.port_rule.low_port}`);
            } else {
              portRange.push(0);
            }
          }
          if (
            tempItem.port_rule.property2 &&
            tempItem.port_rule.high_port != null
          ) {
            if (tempItem.port_rule.low_port !== tempItem.port_rule.high_port) {
              portRange.push(
                `${tempItem.port_rule.low_port}~${tempItem.port_rule.high_port}`
              );
            } else {
              if (!portRange.length) {
                portRange.push(tempItem.port_rule.low_port);
              }
            }
          }
          if (tempItem.port_rule.property3) {
            portRange.push(`${tempItem.port_rule.high_port}~65535`);
          }
          tempItem.portRange = portRange.join(",");
          switch (proRuleItem.port_rule.sign) {
            case 1:
              tempItem.port_rule.portType = ["客户端端口"];
              break;
            case 2:
              tempItem.port_rule.portType = ["服务器端口"];
              break;
            case 3:
              tempItem.port_rule.portType = ["客户端端口", "服务器端口"];
              break;
            default:
              tempItem.port_rule.portType = [];
              break;
          }
          this.proRuleList.push(tempItem);
        }
      }
    },
    // 将表单数据转换为表格可用数据
    formDataToTableData() {
      let proRuleItem = {
        index: Math.floor(Math.random() * 10000),
        pro_id: this.formData.pro_id,
        port_rule: {
          property1: this.formData.port_rule.property1,
          property2: this.formData.port_rule.property2,
          property3: this.formData.port_rule.property3,
          low_port: this.formData.port_rule.low_port,
          high_port: this.formData.port_rule.high_port,
          portType: this.formData.port_rule.portType,
        },
        portRange: "",
      };
      this.id_pro_options.forEach((item) => {
        if (item.id === this.formData.ProID) {
          proRuleItem.ProName = item.protocol_value;
        }
      });
      let portRange = [];
      if (proRuleItem.port_rule.property1) {
        if (
          proRuleItem.port_rule.low_port == null &&
          proRuleItem.port_rule.high_port
        ) {
          proRuleItem.port_rule.low_port = proRuleItem.port_rule.high_port - 1;
        }
        if (proRuleItem.port_rule.low_port) {
          portRange.push(`0~${proRuleItem.port_rule.low_port}`);
        }
        if (proRuleItem.port_rule.low_port === 0) {
          portRange.push(0);
        }
      }
      if (proRuleItem.port_rule.property2) {
        if (
          !proRuleItem.port_rule.high_port &&
          proRuleItem.port_rule.high_port !== 0 &&
          proRuleItem.port_rule.low_port !== 65535
        ) {
          proRuleItem.port_rule.high_port = proRuleItem.port_rule.low_port + 1;
        }
        if (
          !proRuleItem.port_rule.low_port &&
          proRuleItem.port_rule.low_port !== 0
        ) {
          proRuleItem.port_rule.low_port = proRuleItem.port_rule.high_port - 1;
        }
        if (proRuleItem.port_rule.high_port != null) {
          if (
            proRuleItem.port_rule.low_port !== proRuleItem.port_rule.high_port
          ) {
            portRange.push(
              `${proRuleItem.port_rule.low_port}~${proRuleItem.port_rule.high_port}`
            );
          } else {
            if (!portRange.length && !proRuleItem.port_rule.property3) {
              portRange.push(proRuleItem.port_rule.low_port);
            }
          }
        }
      }
      if (proRuleItem.port_rule.property3) {
        portRange.push(
          `${
            proRuleItem.port_rule.high_port === undefined
              ? proRuleItem.port_rule.low_port
              : proRuleItem.port_rule.high_port
          }~65535`
        );
      }
      proRuleItem.portRange = portRange.join(",");
      return proRuleItem;
    },
    addToList() {
      this.$refs["proRule"].validate((valid) => {
        if (valid) {
          this.proRuleList.unshift(this.formDataToTableData());
          this.showDataToRuleParams();
        } else {
          this.$message.error("检验错误，请检查必填项是否填写。");
        }
      });
    },
    // 修改协议规则
    editProRule() {
      this.$refs["proRule"].validate((valid) => {
        if (valid) {
          this.proRuleList = this.proRuleList.map((item, index) => {
            if (item.index === this.formData.index) {
              item = this.formDataToTableData();
            }
            return item;
          });
          this.showDataToRuleParams();
          this.initFormData();
          this.showAddForm = false;
          this.$message.success("保存成功");
        } else {
          this.$message.error("检验错误，请检查必填项是否填写。");
        }
      });
    },
    // 将要修改的协议规则反写到表单里
    editRuleInfo(row) {
      this.formData = JSON.parse(JSON.stringify(row));
      this.showAddForm = true;
      var scrollTop = document.querySelector("#addRules");
      if (scrollTop.scrollTop > 390) {
        scrollTop.scrollTop = 390;
      }
      this.showsave = false;
    },
    deleteRuleInfo(row) {
      console.log(row);
      this.proRuleList.forEach((item, index) => {
        if (item.index === row.index) {
          this.proRuleList.splice(index, 1);
        }
      });
      this.showDataToRuleParams();
    },
    handleIDProFilter(query) {
      if (query === "") {
        this.id_pro_options = this.id_pro_full_options;
      } else {
        this.id_pro_options = this.id_pro_full_options.filter((item) => {
          return (
            item.id.toString().indexOf(query.toLowerCase()) > -1 ||
            item.protocol_value.toLowerCase().indexOf(query.toLowerCase()) > -1
          );
        });
      }
    },
    handleIDPrpVisibleChange(visible) {
      if (!visible) {
        this.id_pro_options = this.id_pro_full_options;
      }
    },
    // 获取协议ID
    initIDProOptions() {
      for (let key in this.dict.app_id) {
        this.id_pro_full_options.push({
          id: parseInt(key),
          protocol_value:
            this.proType[this.dict.app_type_map[parseInt(key)]] +
            " - " +
            this.dict.app_id[key] +
            "(" +
            this.dict.app_value[parseInt(key)] +
            ")",
        });
      }
      this.id_pro_options = this.id_pro_full_options;
    },
    // 激活对应端口范围
    lockPort(num) {
      if (num == 1) {
        this.formData.port_rule.property1 = !this.formData.port_rule.property1;
      }
      if (num == 2) {
        this.formData.port_rule.property2 = !this.formData.port_rule.property2;
      }
      if (num == 3) {
        this.formData.port_rule.property3 = !this.formData.port_rule.property3;
      }
    },
    propertyToValue(property1, property2, property3) {
      let value = 0;
      if (property1 != undefined) value += Number(property1);
      if (property2 != undefined) value += Number(property2) * 2;
      if (property3 != undefined) value += Number(property3) * 4;
      return value;
    },
  },
};
</script>

<style lang="scss" scoped>
.p-relative {
  position: relative;
  .tab-box-btn {
    position: absolute;
    top: 0px;
    right: 20px;

    .el-button {
      border: 1px solid #cecece;
      width: 35px;
      height: 27px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.agreement-box {
  &-top {
    .el-form-item {
      margin: 0;
    }
    .title {
      font-weight: 500;
      font-size: 12px;
      line-height: 16px;
      text-transform: capitalize;
      margin-bottom: 6px;
    }
    ::v-deep .el-input .el-input__inner {
      font-weight: 400;
      font-size: 12px;
      padding-left: 5px;
      height: 27px;
      width: 440px;
    }
    ::v-deep .el-input__suffix {
      height: 54px;
      top: -8px;
    }
  }
  &-mid {
    .title {
      margin: 24.5px 0 6px 0;
    }
    &-box {
      display: flex;
      align-items: center;
      .active {
        color: #116ef9;
      }
      &-input_1 {
        ::v-deep .el-input__inner {
          width: 27px !important;
          height: 27px !important;
          font-weight: 400;
          font-size: 12px;
          padding: 0px;
          text-align: center;
          line-height: 27px;
        }
      }
      &-input_2 {
        ::v-deep .el-input__inner {
          width: 48px !important;
          height: 27px !important;
          font-weight: 400;
          font-size: 12px;
          padding: 0px;
          text-align: center;
          line-height: 27px;
        }
      }
      &-input_3 {
        ::v-deep .el-input__inner {
          width: 56px !important;
          height: 27px !important;
          font-weight: 400;
          font-size: 12px;
          padding: 0px;
          text-align: center;
          line-height: 27px;
        }
      }
      &-input_4 {
        ::v-deep .el-input__inner {
          width: 56px !important;
          height: 27px !important;
          font-weight: 400;
          font-size: 12px;
          padding: 0px;
          text-align: center;
          line-height: 27px;
        }
      }
      .svg-icon {
        margin: 0 7.33px;
      }
      &-radio {
        margin-left: 20px;
      }
    }
  }
  &-down {
    margin-top: 20px;
    .btn {
      ::v-deep .el-button {
        border: none;
      }
    }
  }
}
</style>

<template>
  <div class="filtrate-rule">
    <div>
      <div class="handle-box">
        <div>
          <el-form
            ref="ruleForm"
            :model="ruleForm"
            class="demo-ruleForm"
            @submit.native.prevent
          >
            <div class="handle-box-l">
              <!-- 名称下拉框 -->
              <el-form-item prop="name">
                <div class="nameselect">
                  <el-input
                    v-model.trim="ruleForm.name"
                    placeholder="请输入规则名称"
                  >
                    <el-button slot="append" class="searchBtn" icon="el-icon-search" @click.native="searchButtonClick('ruleForm')"></el-button>
                  </el-input>
                </div>
              </el-form-item>
              <div style="cursor: pointer" @click="showfrom">
                <span style="color: #116ef9; margin-left: 8px; font-size: 14px">{{ handlefrom ? "收起" : "更多条件"
                }}<svg-icon :icon-class="handlefrom ? 'coll2' : 'menu-down'">
                </svg-icon>
                </span>
              </div>
            </div>
          </el-form>
        </div>
        <!-- 功能按钮 -->
        <div class="handle-box-right">
          <el-popover
            placement="bottom"
            trigger="click"
            :visible-arrow="false"
            popper-class="alldownbox"
            :disabled="downloadDisable"
          >
            <div
              slot="reference"
              :class="downloadDisable ? 'alldown' : 'qlactive'"
            >
              <div class="alldown-l">删除</div>
              <div class="alldown-r">
                <svg-icon icon-class="del-down2" />
              </div>
            </div>
            <div class="alldownfoot">
              <el-button class="alldownfoot-t" @click="DeleteRules">
                删除选中项
              </el-button>
              <el-button class="alldownfoot-d" @click="AllDeleteRules">
                删除全部项
              </el-button>
            </div>
          </el-popover>
          <el-button class="ml8" plain @click="UPLOAD_CLICK"> 导入 </el-button>
          <el-button plain @click="DOWNLOAD_CLICK"> 导出 </el-button>
          <el-button plain @click="Allupules"> 全部导出 </el-button>
          <el-button icon="el-icon-plus" type="primary" @click="openDrawer">
            添加
          </el-button>
        </div>
      </div>
      <div v-if="handlefrom" class="handle-from">
        <el-form ref="ruleForm" :model="ruleForm" class="demoRuleForm">
          <div class="handle-from-box">
            <!-- ID输入框 -->
            <el-form-item prop="idtext" label="规则ID:">
              <el-input
                v-model="ruleForm.idtext"
                placeholder="ID"
                oninput="value=value.replace(/[^\d]/g,'')"
              >
              </el-input>
            </el-form-item>
          </div>
          <div class="handle-from-box">
            <!-- 规则类型复选框 -->
            <el-form-item prop="rulename" label="类型:" class="ruleselectbox">
              <el-select
                v-model="ruleForm.rulename"
                multiple
                collapse-tags
                placeholder="规则类型"
                :popper-append-to-body="false"
                :remote="false"
                :reserve-keyword="false"
                :filter-method="single ? null : filterMethodThrottle"
                :loading="loading"
              >
                <div class="inputbox">
                  <el-input
                    v-model="filterQuery"
                    placeholder="关键字"
                    style="z-index: 99999"
                    @input="filterMethodThrottle($event)"
                  >
                    <svg-icon slot="suffix" icon-class="searchicon" />
                  </el-input>
                </div>
                <template>
                  <el-option
                    v-for="(value, key) in ruleTypeData"
                    :key="value"
                    :label="key"
                    :value="key"
                  >
                  </el-option>
                </template>
              </el-select>
              <div v-show="omitshow" class="omit-box">
                <svg-icon icon-class="omitIcon" class="omit-box-icon" />
              </div>
            </el-form-item>
          </div>
          <div class="handle-from-box">
            <!-- 状态 -->
            <el-form-item prop="state" label="状态:">
              <el-select v-model="ruleForm.state" placeholder="状态">
                <el-option
                  v-for="item in stateData"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="handle-from-box">
            <!-- 规则内容 -->
            <el-form-item prop="rulemain" label="内容:">
              <el-input
                v-model="ruleForm.rule_data"
                placeholder="规则内容"
              ></el-input>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>

    <div class="table-box">
      <el-table
        :data="rulesTableData"
        fit
        show-overflow-tooltip
        tooltip-effect="light"
        border
        stripe
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column type="selection" width="50" align="center" fixed>
        </el-table-column>
        <el-table-column
          fixed
          type="index"
          :index="indexMethod"
          label="序号"
          width="70"
        />
        <el-table-column fixed prop="rule_id" label="规则ID" min-width="100">
        </el-table-column>
        <el-table-column prop="rule_name" label="名称" min-width="130">
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                {{ scope.row.rule_name }}
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    popper-class="sortpopover"
                  >
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button @click="searchRulePos(scope.row)">
                        <svg-icon
                          icon-class="sort-up"
                          style="margin-right: 15px"
                        />正向检索
                      </el-button>
                      <el-button @click="searchRuleNeg(scope.row)">
                        <svg-icon
                          icon-class="sort-down"
                          style="margin-right: 15px"
                        />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="rule_desc" label="描述"> </el-table-column>
        <el-table-column prop="rule_level" label="等级"> </el-table-column>
        <el-table-column prop="capture_mode" label="采集模式" min-width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.capture_mode }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="lib_respond_open"
          label="程序响应"
          min-width="120"
        >
          <template slot-scope="scope">
            {{ scope.row.lib_respond_open ? "开启" : "关闭" }}
          </template>
        </el-table-column>
        <el-table-column prop="rulename" label="规则类型" min-width="120">
          <template slot="header">
            <tableRuleFilter
              ref="ruleFilter"
              prop="rulename"
              label="规则类型"
              :order_prop="order.order_prop"
              :reset_stamp="reset_stamp"
              @filterHandler="ruleFilterHandler"
            />
          </template>
        </el-table-column>

        <el-table-column
          min-width="120"
          prop="updated_time"
          label="修改时间"
          @sort-change="handleSortChange"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.updated_time }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="total_sum_bytes"
          label="命中数据量"
          sortable="custom"
          min-width="140"
          @sort-change="handleSortChange"
        >
        </el-table-column>
        <el-table-column
          prop="last_size_time"
          label="数据命中时间"
          sortable="custom"
          min-width="190"
          @sort-change="handleSortChange"
        >
          <template slot-scope="scope">
            <div>
              {{ scope.row.last_size_time ? scope.row.last_size_time : "--" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="rule_state" label="状态变更" min-width="120">
          <template slot="header">
            <table-state-filter
              ref="stateFilter"
              prop="rule_state"
              label="状态变更"
              :order_prop="order.order_prop"
              :reset_stamp="reset_stamp"
              @filterHandler="stateFilterHandler"
            />
          </template>
          <template slot-scope="scoped">
            <div
              :class="
                scoped.row.rule_state == '生效' ? 'rulestate' : 'rulestate1'
              "
            >
              <el-select
                v-model="scoped.row.rule_state"
                placeholder="请选择"
                size="mini"
                @change="editState(scoped.row)"
              >
                <el-option
                  v-for="item in options1"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template slot-scope="scope">
            <!-- <svg-icon
              icon-class="icon_16_edit"
              class="handle-icon"
              style="cursor: pointer"
              @click="openDrawer(scope.row)"
            ></svg-icon>
            <svg-icon
              icon-class="icon_16_delete"
              class="handle-icon"
              style="margin-left: 15px; cursor: pointer"
              @click="deleteAloneRules(scope.row)"
            ></svg-icon> -->
            <el-button type="text" @click="openDrawer(scope.row)">
              编辑
            </el-button>
            <el-button
              class="dividing"
              type="text"
              @click="deleteAloneRules(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="current_page-box">
        <el-pagination
          :current-page="listParam.current_page"
          :page-sizes="[5, 10, 20, 30, 50]"
          :page-size="listParam.page_size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="rulesTotal"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>

    <!-- 上传文件弹窗 -->
    <el-dialog
      :visible.sync="uploadDiaglog"
      :before-close="UPLOADDIALOG_CLOSE"
      class="upload-diglog"
      :close-on-click-modal="false"
      :show-close="false"
    >
      <div slot="title">
        <div>上传采集规则</div>
        <div @click="templateDownn">规则模板.CSV</div>
        <div></div>
      </div>
      <el-upload
        v-show="fileList.length === 0"
        ref="upload"
        class="upload-demo"
        drag
        :headers="uploadHeaders"
        :action="isOffLine ? uploadUrlOffline : uploadUrl"
        :data="uploadData"
        multiple
        :show-file-list="false"
        :on-change="UPLOAD_CHANGE"
        :before-upload="BEFORE_UPLOAD"
        :on-success="UPLOAD_SUCCESS"
        :on-error="UPLOAD_ERROR"
        accept=".csv"
      >
        <svg-icon icon-class="upload-file" class="upload-file-icon"></svg-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="text-maxsize">文件不能大于25M</div>
      </el-upload>
      <div v-show="fileList.length > 0" class="upload-timing">
        <div class="uploading-box">
          <div>
            <span>{{ fileName }}</span>
            <span>{{ uploadStatusText }}</span>
          </div>
          <div>
            <el-progress
              :percentage="uploadPlan"
              color="#39D979"
              :show-text="false"
            ></el-progress>
          </div>
          <svg-icon
            v-if="uploadPlan === 100"
            class="success-icon"
            icon-class="upload-success"
            color="#a4efa4"
          >
          </svg-icon>
        </div>
        <div v-if="uploadPlan === 100">
          上传规则总数：<span style="color: #116ef9">{{ uploadTotal }}</span>个; 导入成功规则：<span style="color: #39d979">{{
            uploadSucNum
          }}</span>个; 导入失败规则：<span style="color: red">{{ uploadFailNum }}</span>个;
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="cancel" @click="UPLOAD_CANCEL">取 消</el-button>
        <el-button
          type="primary"
          :class="uploadPlan > 0 && uploadPlan < 100 ? 'submit-d' : 'submit'"
          @click="UPLOAD_SUBMIT"
        >确 定</el-button>
      </span>
    </el-dialog>
    <AddRulr
      ref="drawer"
      size="60%"
      commit-btn-text="审核通过"
      :dialog="opnedialog"
      :rule-data="tempEdit"
      :is-off-line="isOffLine"
      :offline-id="offlineId"
      @closedrawerdate="getdrawerdate"
      @closeAddDrawer="closeDrawer"
    />
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import tableStateFilter from "../components/Rulrfile/table_state_filrter";
import tableRuleFilter from "../components/Rulrfile/table_Rule_Filter";
import axios from "axios";
import { formatNumber, parseTimefake } from "@/utils";
import api from "@/api/offline";
import mixins from "@/views/offline/mixins";
import {
  GetRulrList,
  StateSet,
  DeleteRule,
  Download_Rule,
} from "@/api/AddRulrs/AddRulr";

import AddRulr from "./Rulrfile/AddRulr.vue";
import FileSaver from "file-saver";
import SvgIcon from "@/components/SvgIcon/index.vue";
export default {
  name: "CollectRule",
  components: {
    AddRulr,
    tableStateFilter,
    tableRuleFilter,
    SvgIcon,
  },
  mixins: [mixins],
  data() {
    return {
      downloadDisable: false,
      handlefrom: false,
      uploadHeaders: { token: getToken() },
      // 存储文件上传时的状态
      fileresponse: {},
      task_id: localStorage.getItem("task_id"),
      options1: [
        {
          name: "失效",
          value: "失效",
        },
        {
          name: "生效",
          value: "生效",
        },
      ],
      ruleForm: {
        idtext: "",
        name: "",
        rulename: [],
        state: "",
        rule_data: "",
      },

      // 搜索数据
      searchData: {
        idtext: "",
        name: "",
        rulename: [],
        state: "",
      },
      omitshow: false,
      filterQuery: "",
      value: "",
      rulesTableData: [],
      rulesTotal: 0,
      listParam: {
        page_size: 10,
        current_page: 1,
        order_field: "updated_time",
        asc: false,
        sort_order: "desc", //默认 desc 排序方式：desc/asc
      },
      capturearr: [],
      ruleTypeData: {
        IP规则: "ip_rules",
        协议规则: "pro_rules",
        特征字规则: "key_rules",
        正则规则: "regex_rules",
        域名规则: "domain_rules",
      },

      ruleTypeNum: {
        IP规则: "1",
        协议规则: "2",
        特征字规则: "3",
        正则规则: "4",
        域名规则: "5",
      },
      ruleTypeNumE: {
        ip_rules: "1",
        pro_rules: "2",
        key_rules: "3",
        regex_rules: "4",
        domain_rules: "5",
      },
      stateData: ["生效", "失效"],
      order: {
        order_prop: "",
      },
      reset_stamp: 0,
      multipleSelection: [],
      tempEdit: {},
      oldlist: [],
      optionslist: [],
      loading: false,
      value: "",
      value2: [],
      opnedialog: false,
      mainrule: "",
      idtext: "",
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      pageTotal: 4,
      single: true,
      // 上传相关
      uploadDiaglog: false,
      uploadUrl:
        process.env.VUE_APP_BASE_API === ""
          ? `${process.env.VUE_APP_BASE_API}/api/feature/import`
          : `${process.env.VUE_APP_BASE_API}/feature/import`,
      uploadUrlOffline:
        process.env.VUE_APP_BASE_API === ""
          ? `${process.env.VUE_APP_BASE_API}/api/offline/feature/import`
          : `${process.env.VUE_APP_BASE_API}/offline/feature/import`,
      fileList: [],
      fileName: "",
      uploadData: {
        task_id: this.task_id,
        batch_id: this.task_id == 0 ? 1000001 : 1000002,
      }, //上传数据
      // 上传进度条相关
      uploadPlan: 10,
      uploadStatusText: "上传中！",
      uploadSucNum: 0,
      uploadFailNum: 0,
      uploadTotal: 0,
      timer: null,
      capturemodeData: [],
    };
  },
  computed: {
    capturemode() {
      let newarr = [];
      for (var key in this.$store.state.long.Dict.capture_mode) {
        let tempMode = {};
        tempMode.label = this.$store.state.long.Dict.capture_mode[key];
        tempMode.value = parseInt(key);
        newarr.push(tempMode);
      }
      return newarr;
    },
  },
  watch: {
    offlineId: {
      handler(val) {
        if (val && this.isOffLine) {
          this.uploadData.task_id = this.offlineId;
          this.initData();
        }
      },
      immediate: true,
    },
  },
  mounted() {
    if (!this.isOffLine) {
      this.task_id = localStorage.getItem("task_id");
      this.uploadData.task_id = this.task_id;
      this.timer = setTimeout(() => {
        this.initData();
      }, 500);
    }
  },
  beforeDestroy() {
    !this.isOffLine && clearTimeout(this.timer);
  },
  methods: {
    showfrom() {
      this.handlefrom = !this.handlefrom;
    },
    // 初始化数据
    initData() {
      let param = {
        rule_id: +this.searchData.idtext || "",
        rule_name: this.searchData.name,
        rule_state: this.ruleForm.state,
        rule_type_filter: [],
        rule_data: this.ruleForm.rule_data,
        current_page: this.listParam.current_page,
        page_size: this.listParam.page_size,
        order_field: this.listParam.order_field,
        sort_order: this.listParam.sort_order,
      };
      this.searchData.rulename.forEach((item) => {
        param.rule_type_filter.push(this.ruleTypeNum[item]);
      });
      if (this.order.order_prop) {
        param[this.order.order_prop] = this.order[this.order.order_prop];
      }
      if (this.isOffLine) {
        if (this.offlineId) {
          param.task_id = this.offlineId;
          api.offlineFeatureList(param).then((res) => {
            if (res.err == 0) {
              this.rulesTotal = res.data.total;
              this.rulesTableData = res.data.records;
              for (let i in this.rulesTableData) {
                if (this.rulesTableData[i].last_size_time) {
                  // 数据命中时间格式化
                  this.rulesTableData[i].last_size_time = parseTimefake(
                    this.rulesTableData[i].last_size_time
                  );
                }
                // 命中量格式化
                this.rulesTableData[i].total_sum_bytes = formatNumber(
                  this.rulesTableData[i].total_sum_bytes
                );
                // 创建时间格式化
                this.rulesTableData[i].updated_time = parseTimefake(
                  this.rulesTableData[i].updated_time
                );
                var ruleType = [];
                this.capturemode.forEach((item) => {
                  if (item.value == this.rulesTableData[i].capture_mode) {
                    this.rulesTableData[i].capture_mode = item.label;
                  }
                });
                for (let key in this.rulesTableData[i]) {
                  for (let ruleName in this.ruleTypeData) {
                    if (
                      this.ruleTypeData[ruleName] === key &&
                      this.rulesTableData[i][key] !== null
                    ) {
                      ruleType.push(ruleName);
                    }
                  }
                }
                this.rulesTableData[i].rulename = ruleType.join(", ");
              }
            } else {
              this.$message.error(res.msg);
            }
          });
        }
      } else {
        param.task_id = this.task_id;
        GetRulrList(param).then((res) => {
          if (res.err == 0) {
            this.rulesTotal = res.data.total;
            this.rulesTableData = res.data.records;
            for (let i in this.rulesTableData) {
              if (this.rulesTableData[i].last_size_time) {
                // 数据命中时间格式化
                this.rulesTableData[i].last_size_time = parseTimefake(
                  this.rulesTableData[i].last_size_time
                );
              }
              // 命中量格式化
              this.rulesTableData[i].total_sum_bytes = formatNumber(
                this.rulesTableData[i].total_sum_bytes
              );
              // 创建时间格式化
              this.rulesTableData[i].updated_time = parseTimefake(
                this.rulesTableData[i].updated_time
              );
              var ruleType = [];
              this.capturemode.forEach((item) => {
                if (item.value == this.rulesTableData[i].capture_mode) {
                  this.rulesTableData[i].capture_mode = item.label;
                }
              });
              for (let key in this.rulesTableData[i]) {
                for (let ruleName in this.ruleTypeData) {
                  if (
                    this.ruleTypeData[ruleName] === key &&
                    this.rulesTableData[i][key] !== null
                  ) {
                    ruleType.push(ruleName);
                  }
                }
              }
              this.rulesTableData[i].rulename = ruleType.join(", ");
            }
          } else {
            this.$message.error(res.msg);
          }
        });
      }
    },
    // 获取到子组件传回来的关闭drawe标志
    getdrawerdate(e) {
      this.opnedialog = e;
    },
    // 搜索表单按钮
    searchButtonClick() {
      this.listParam.current_page = 1;
      this.listParam.asc = false;
      this.$refs.stateFilter.initData();
      this.$refs.ruleFilter.initData();
      this.order = {
        order_prop: "",
      };
      Object.assign(this.searchData, this.ruleForm);
      this.initData();
    },
    // 重置搜索表单
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.ruleForm = {
        ruleForm: {
          idtext: "",
          name: "",
          rulename: [],
          state: "",
          rule_data: "",
        },
      };
      this.searchData = {
        idtext: "",
        name: "",
        rulename: [],
        state: "",
      };
      this.searchButtonClick();
    },
    // 排序
    handleSortChange({ column, prop, order }) {
      this.listParam.order_field = prop;
      this.listParam.asc = order === "descending" ? false : true;
      this.listParam.sort_order = order === "descending" ? "desc" : "asc";
      this.initData();
    },
    openDrawer(row) {
      this.opnedialog = true;
      this.tempEdit = row || {};
    },
    filterMethodThrottle(query) {
      console.log(query);
      let searchval = query.toLowerCase();
      console.log(searchval);
      let newlistdata = [];
      if (searchval !== "") {
        console.log("这里的数据不为空");
        // setTimeout(() => {
        // console.log(this.ruleTypeData)
        // this.ruleTypeData.filter((item) => {
        //   console.log(item)
        //   // if (item.label.toLowerCase().indexOf(searchval) !== -1) {
        //   //   if (item) {
        //   //     newlistdata.push(item);
        //   //   } else {
        //   //     return;
        //   //   }
        //   // }
        // });
        // console.log(newlistdata);
        // this.ruleTypeData = newlistdata;
        // }, 500);
      } else {
        this.list = this.oldlist;
      }
    },
    // 分页相关方法
    handleSizeChange(val) {
      this.listParam.page_size = val;
      this.initData();
    },
    handleCurrentChange(val) {
      this.listParam.current_page = val;
      this.initData();
    },
    // 修改状态
    editState(row) {
      let param = {
        id: row.id,
        rule_state: row.rule_state,
        task_id: this.task_id,
        batch_id:
          this.task_id == "0"
            ? 1000001
            : this.task_id == "1"
              ? 1000002
              : 1000001,
      };
      if (this.isOffLine) {
        if (this.offlineId) {
          param.task_id = this.offlineId;
          api.offlineFeatureState(param).then((res) => {
            if (res.err === 0) {
              this.initData();
            } else {
              this.$message.error(`状态修改失败${res.msg}`);
            }
          });
        }
      } else {
        param.task_id = this.task_id;
        StateSet(param).then((res) => {
          if (res.err === 0) {
            this.initData();
          } else {
            this.$message.error(`状态修改失败${res.msg}`);
          }
        });
      }
    },
    // 点击删除某一行
    deleteAloneRules(row) {
      this.$Notice({
        title: "删除提示",
        type: "warn",
        message: "确认删除选中的规则？",
      })
        .then(() => {
          if (this.isOffLine) {
            if (this.offlineId) {
              api
                .offlineFeatureDelete({
                  ids: [row.id],
                  task_id: this.offlineId,
                  batch_id: 1000001,
                })
                .then((res) => {
                  if (res.err == 0) {
                    let leftNum = this.rulesTotal - res.data;
                    while (
                      this.listParam.current_page > 1 &&
                      (this.listParam.current_page - 1) *
                        this.listParam.page_size +
                        1 >
                        leftNum
                    ) {
                      this.listParam.current_page -= 1;
                    }
                    this.initData();
                    this.$message.success(res.msg);
                  }
                });
            }
          } else {
            DeleteRule({
              ids: [row.id],
              task_id: this.task_id,
              batch_id: 1000001,
            }).then((res) => {
              if (res.err == 0) {
                let leftNum = this.rulesTotal - res.data;
                while (
                  this.listParam.current_page > 1 &&
                  (this.listParam.current_page - 1) * this.listParam.page_size +
                    1 >
                    leftNum
                ) {
                  this.listParam.current_page -= 1;
                }
                this.initData();
              }
            });
          }
        })
        .catch(() => {
          // on cancel
        });
    },
    // 获取选择到的规则
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 删除多个选中的规则
    DeleteRules() {
      if (this.multipleSelection.length > 0) {
        this.$Notice({
          title: "删除提示",
          type: "warn",
          message: `确认删除选中的${this.multipleSelection.length}条规则？`,
        })
          .then(() => {
            let DeleteRulesarr = [];
            for (let item of this.multipleSelection) {
              DeleteRulesarr.push(item.id);
            }
            if (this.isOffLine) {
              if (this.offlineId) {
                api
                  .offlineFeatureDelete({
                    ids: DeleteRulesarr,
                    task_id: this.offlineId,
                    batch_id: 1000001,
                  })
                  .then((res) => {
                    if (res.err == 0) {
                      let leftNum = this.rulesTotal - res.data;
                      while (
                        this.listParam.current_page > 1 &&
                        (this.listParam.current_page - 1) *
                          this.listParam.page_size +
                          1 >
                          leftNum
                      ) {
                        this.listParam.current_page -= 1;
                      }
                      this.initData();
                      this.$message.success(res.msg);
                    }
                  });
              }
            } else {
              DeleteRule({
                ids: DeleteRulesarr,
                task_id: this.task_id,
                batch_id: 1000001,
              }).then((res) => {
                if (res.err == 0) {
                  let leftNum = this.rulesTotal - res.data;
                  while (
                    this.listParam.current_page > 1 &&
                    (this.listParam.current_page - 1) *
                      this.listParam.page_size +
                      1 >
                      leftNum
                  ) {
                    this.listParam.current_page -= 1;
                  }
                  this.initData();
                  this.$message.success(res.msg);
                }
              });
            }
          })
          .catch(() => {});
      } else {
        this.$Notice({
          title: "删除提示",
          type: "warn",
          // message: "确认删除选中的条目？",
          message: "请至少选择一条数据进行删除。",
        })
          .then(() => {})
          .catch(() => {});
      }
    },
    // 全部删除
    AllDeleteRules() {
      this.$Notice({
        title: "删除提示",
        type: "warn",
        message: `是否删除所有规则？`,
      })
        .then(() => {
          if (this.isOffLine) {
            if (this.offlineId) {
              api
                .offlineFeatureDelete({
                  ids: null,
                  task_id: this.offlineId,
                  batch_id: 1000001,
                })
                .then((res) => {
                  if (res.err == 0) {
                    let leftNum = this.rulesTotal - res.data;
                    while (
                      this.listParam.current_page > 1 &&
                      (this.listParam.current_page - 1) *
                        this.listParam.page_size +
                        1 >
                        leftNum
                    ) {
                      this.listParam.current_page -= 1;
                    }
                    this.initData();
                    this.$message.success(res.msg);
                  }
                });
            }
          } else {
            DeleteRule({
              ids: null,
              task_id: this.task_id,
              batch_id: 1000001,
            }).then((res) => {
              if (res.err == 0) {
                let leftNum = this.rulesTotal - res.data;
                while (
                  this.listParam.current_page > 1 &&
                  (this.listParam.current_page - 1) * this.listParam.page_size +
                    1 >
                    leftNum
                ) {
                  this.listParam.current_page -= 1;
                }
                this.initData();
                this.$message.success(res.msg);
              }
            });
          }
        })
        .catch(() => {});
    },
    // 全部导出
    Allupules() {
      console.log(this.multipleSelection);
      this.$Notice({
        title: "导出提示",
        type: "warn",
        message: `是否导出所有规则？`,
      })
        .then(() => {
          let baseURL;
          let task_id;
          if (this.isOffLine) {
            if (this.offlineId) {
              task_id = this.offlineId;
              if (process.env.VUE_APP_BASE_API === "") {
                baseURL = `${process.env.VUE_APP_BASE_API}/api//offline/feature/getCsv`;
              } else {
                baseURL =
                  process.env.VUE_APP_BASE_API + "/offline/feature/getCsv";
              }
            }
          } else {
            task_id = this.task_id;
            if (process.env.VUE_APP_BASE_API === "") {
              baseURL = `${process.env.VUE_APP_BASE_API}/api/feature/getCsv`;
            } else {
              baseURL = process.env.VUE_APP_BASE_API + "/feature/getCsv";
            }
          }
          axios({
            method: "POST",
            url: baseURL, // 后端下载接口地址
            responseType: "blob", // 设置接受的流格式
            headers: {
              token: `${getToken()}`,
            },
            data: {
              task_id: task_id,
            },
          }).then((res) => {
            console.log(res.data);
            if (res.err === 40005) {
              this.$message.error(res.msg);
            } else {
              const blob = new Blob([res.data], { type: "application/json" });
              FileSaver.saveAs(blob, res.headers["content-disposition"]);
            }
          });
        })
        .catch(() => {});
    },
    closeDrawer(val) {
      if (val === "refresh") {
        this.initData();
      }
      this.opnedialog = false;
    },
    // 规则类型排序
    ruleFilterHandler(prop, rule, sort) {
      console.log(prop, rule, sort);
      this.order.order_prop = "rule_type_filter";
      if (rule && rule.length) {
        let newarr = [];
        rule.forEach((item) => {
          console.log(item);
          newarr.push(this.ruleTypeNumE[item]);
        });
        this.order["rule_type_filter"] = newarr;
      } else {
        this.order["rule_type_filter"] = [];
      }
      if (sort != null) this.listParam.asc = sort;
      this.initData();
    },
    // 状态变更排序
    stateFilterHandler(prop, state, sort) {
      this.order.order_prop = prop;
      this.listParam.order_field = prop;
      if (state != null) this.order[prop] = state;
      if (sort != null) this.listParam.asc = sort;
      this.initData();
    },
    // 点击下载
    DOWNLOAD_CLICK() {
      let DeleteRulesarr = [];
      for (let item of this.multipleSelection) {
        DeleteRulesarr.push(item.id);
      }
      if (DeleteRulesarr.length < 1) {
        this.$message.error("请选择规则！");
      } else {
        let baseURL;
        let task_id;
        if (this.isOffLine) {
          if (this.offlineId) {
            task_id = this.offlineId;
            if (process.env.VUE_APP_BASE_API === "") {
              baseURL = `${process.env.VUE_APP_BASE_API}/api//offline/feature/getCsv`;
            } else {
              baseURL =
                process.env.VUE_APP_BASE_API + "/offline/feature/getCsv";
            }
          }
        } else {
          task_id = this.task_id;
          if (process.env.VUE_APP_BASE_API === "") {
            baseURL = `${process.env.VUE_APP_BASE_API}/api/feature/getCsv`;
          } else {
            baseURL = process.env.VUE_APP_BASE_API + "/feature/getCsv";
          }
        }

        axios({
          method: "POST",
          url: baseURL, // 后端下载接口地址
          responseType: "blob", // 设置接受的流格式
          headers: {
            token: `${getToken()}`,
          },
          data: {
            ids: DeleteRulesarr,
            task_id: task_id,
          },
        }).then((res) => {
          if (res.err === 40005) {
            this.$message.error(res.msg);
          } else {
            const blob = new Blob([res.data], { type: "application/json" });
            FileSaver.saveAs(blob, res.headers["content-disposition"]);
          }
        });
      }
    },
    // 点击上传按钮
    UPLOAD_CLICK() {
      this.uploadPlan = 0;
      this.fileList = [];
      this.uploadStatusText = "上传中！";
      this.uploadDiaglog = true;
    },
    // 上传弹窗点击取消
    UPLOAD_CANCEL() {
      if (this.uploadPlan > 0 && this.uploadPlan < 100) {
        this.$refs.upload.abort();
        this.$refs.upload.clearFiles();
        this.fileList = [];
        this.uploadPlan = 0;
        this.uploadStatusText = "上传中！";
        this.$message.warning("已取消上传！");
      } else {
        this.uploadDiaglog = false;
      }
    },
    // 上传弹窗关闭时
    UPLOADDIALOG_CLOSE(done) {
      this.$refs.upload.abort();
      this.uploadPlan = 0;
      this.fileList = [];
      this.uploadStatusText = "上传中！";
      done();
    },
    // 上传文件状态改变时的钩子
    UPLOAD_CHANGE(file, fileList) {
      console.log(file, fileList, "上传文件状态改变时的钩子");
      this.fileresponse = file;
      this.fileName = file.name;
      if (fileList.length > 0) {
        this.fileList = fileList;
      }
    },
    // 上传文件前的回调
    BEFORE_UPLOAD() {
      this.uploadStatusText = "上传中！";
      this.uploadPlan = 30;
    },
    // 上传文件成功时的回调
    UPLOAD_SUCCESS(res, file, fileList) {
      console.log(res);
      if (res.err === 0) {
        this.uploadPlan = 100;
        this.uploadStatusText = "上传完成！";
        this.uploadSucNum = res.data.sucNum;
        this.uploadFailNum = res.data.failNum;
        this.uploadTotal = res.data.totalNum;
        this.$message.success("上传成功！");
        this.initData();
      } else {
        this.$message.error(res.msg);
        this.$refs.upload.abort();
        this.$refs.upload.clearFiles();
        this.fileList = [];
        this.uploadPlan = 0;
      }
    },
    // 上传失败时的回调
    UPLOAD_ERROR(res) {
      console.log(res);
      this.$message.error("上传失败！");
    },
    // 上传弹窗点击确定
    UPLOAD_SUBMIT() {
      this.uploadDiaglog = false;
    },
    // 正向规则检索
    searchRulePos(row) {
      row.neg = false;
      console.log(row);
      this.$router.push({
        path: "/SessionAnalyse",
        query: { searchDataPos: row },
      });
      // this.$store.commit("conversational/searchDataPos", row);
      // sessionStorage.setItem("searchDataPos", JSON.stringify(row));
      // window.open(pathInfo.href, "_blank");
    },
    // 反向规则检索
    searchRuleNeg(row) {
      row.neg = true;
      // let pathInfo = this.$router.resolve({
      //   path: "/example/table",
      // });
      this.$router.push({
        path: "/SessionAnalyse",
        query: { searchDataPos: row },
      });

      // sessionStorage.setItem("searchDataPos", JSON.stringify(row));
      // this.$store.commit("conversational/searchDataPos", row);
      // window.open(pathInfo.href, "_blank");
    },
    // 模板下载
    templateDownn() {
      let baseURL;
      if (process.env.VUE_APP_BASE_API === "") {
        baseURL = `${process.env.VUE_APP_BASE_API}/api`;
      } else {
        baseURL = process.env.VUE_APP_BASE_API;
      }
      axios({
        method: "GET",
        url: baseURL + "/feature/template", // 后端下载接口地址
        responseType: "blob", // 设置接受的流格式
        headers: {
          token: `${getToken()}`,
        },
      }).then((res) => {
        console.log(res.data);
        if (res.err === 40005) {
          this.$message.error(res.msg);
        } else {
          const blob = new Blob([res.data], { type: "application/json" });
          FileSaver.saveAs(blob, res.headers["content-disposition"]);
        }
      });
    },
    indexMethod(index) {
      return (
        (this.listParam.current_page - 1) * this.listParam.page_size + index + 1
      );
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-table--scrollable-x .el-table__body-wrapper {
  overflow-x: auto !important;
}

.filtrate-rule {
  width: 100%;

  ::v-deep .el-drawer__body {
    overflow: auto;
  }

  .el-drawer__container ::-webkit-scrollbar {
    display: none;
  }

  .handle-box {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 8px;

    &-l {
      display: flex;
      align-items: center;

      ::v-deep .el-input {
        font-weight: 400;
        font-size: 12px;
      }

      .el-form-item {
        padding: 0;
        margin: 0;
      }

      ::v-deep .el-form-item__content {
        margin: 0 !important;
      }

      ::v-deep .el-input__inner {
        height: 32px !important;
      }

      .nameselect {
        width: 256px;
        .el-input {
          font-size: 12px;  
        }
        .searchBtn{
          width: 40px;
        }
      }
    }

    &-r {
      .el-button--primary {
        width: 81px;
        height: 28px;
        background: #116ef9;
        border-radius: 4px;
      }

      .el-button--success {
        height: 28px;
        background: #06c251;
        border-radius: 4px;
      }
    }

    &-right {
      display: flex;
      align-items: center;
      .alldown {
        cursor: not-allowed;
        position: relative;
        height: 32px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        display: flex;
        align-items: center;
        color: #116ef9;
        &-l {
          font-weight: 400;
          font-size: 14px;
          width: 60px;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          border-right: 1px solid #116ef9;
        }

        &-r {
          width: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: 400;
          font-size: 14px;
        }
      }

      .qlactive {
        cursor: pointer;
        position: relative;
        height: 32px;
        border: 1px solid #116ef9;
        border-radius: 4px;
        display: flex;
        align-items: center;
        color: #116ef9;

        &-l {
          font-weight: 400;
          font-size: 14px;
          width: 60px;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          border-right: 1px solid #8abcff;
        }

        &-r {
          width: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: 400;
          font-size: 14px;
        }
      }

      .qlactive:hover {
        color: #409eff;
        border-color: #c6e2ff;
        background-color: #ecf5ff;
      }

      .line::after {
        margin: 0 8px;
        content: "";
        display: block;
        width: 1px;
        height: 16px;
        background: #dee0e7;
      }
    }
  }

  .handle-from {
    margin: 8px 0 16px 0;
    padding: 0 16px;
    display: flex;
    width: 100%;
    height: 64px;
    background: #f7f8fa;
    border-radius: 8px;

    ::v-deep .el-input__inner {
      height: 32px !important;
    }

    ::v-deep .el-form-item__content {
      margin: 0 !important;
    }
    .demo-ruleForm {
      width: 100%;
    }
    .demoRuleForm {
      width: 100%;
      display: flex;

      .handle-from-box {
        width: 100%; // 添加这行
        display: flex; // 添加这行
        flex-wrap: wrap; // 添加这行，允许换行
        padding: 16px 0px;
        .el-form-item {
          width: 100%;
          .el-select,
          .el-input,
          .el-date-editor {
            width: 80%;
          }
        }
      }
    }
    &-box {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .idinput {
        max-width: 256px;

        .el-input {
          width: 100%;
          margin-right: 12px;
        }
      }

      .ruleselect {
        max-width: 256px;
        position: relative;

        .el-select {
          width: 100%;
          // margin-left: 12px;

          ::v-deep .el-scrollbar {
            display: block !important;
          }
        }
      }

      .inputbox {
        display: flex;
        justify-content: center;
        margin-bottom: 8px;

        .el-input {
          width: 250px;
        }

        ::v-deep {
          .el-input__suffix {
            // height: 100%;
            top: -1px;
            font-weight: 400;
            font-size: 12px;
          }
        }
      }

      .omit-box {
        z-index: 9999;
        position: absolute;
        top: 12px;
        right: 35px;
        width: 22px;
        height: 18px;
        background: #767684;
        border-radius: 8px;
        display: flex;
        justify-content: center;
        align-items: center;

        &-icon {
          font-weight: bolder;
        }
      }

      ::v-deep {
        .el-select-dropdown__item {
          border-bottom: 1px solid #f2f3f7;
        }

        .el-select-dropdown.is-multiple
          .el-select-dropdown__item.selected::after {
          content: "";
        }

        .el-tag {
          display: flex;
          justify-content: space-evenly;
          align-items: center;
          color: #ffffff;
          font-size: 10px;
          // width: 65px;
          height: 18px;
          border-radius: 8px;
          background: #767684;
        }

        .el-tag__close {
          margin-top: 1px;
          padding-right: 8px;
          background: none;
          color: #ffffff;
          font-weight: bolder;
        }

        .el-input__suffix {
          height: 54px;
          top: -8px;
          font-weight: 400;
          font-size: 12px;
        }
      }

      .stateselect {
        max-width: 256px;

        .el-select {
          width: 100%;
        }
      }

      .contentrule {
        max-width: 256px;

        .el-input {
          width: 100%;
        }
      }
    }
  }

  .table-box {
    width: 100%;

    &-div {
      height: 1px;
    }

    .sortbox {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      // position: relative;
      .top {
        margin-right: 5px;
      }

      .down {
        width: 10px;
        height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .sorttoole {
        display: none;
        padding-bottom: 5px;
        color: #116ef9;
        // position: absolute;
        // top: -2px;
        // right: 0;
      }
    }

    .sortbox:hover {
      .sorttoole {
        display: block;
        cursor: pointer;
      }
    }

    .current_page-box {
      z-index: 999;
      padding: 10px 0;
      position: sticky;
      right: 34px;
      bottom: 0px;
      display: flex;
      align-items: center;
      // margin-bottom: 20;
      background: #ffffff;
      border-radius: 8px;
      justify-content: flex-end;
    }

    .el-tag {
      cursor: pointer;
    }

    .el-table {
      width: 100%;

      .el-table__header-wrapper table,
      .el-table__body-wrapper table {
        width: 100% !important;
      }

      .el-table__body,
      .el-table__footer,
      .el-table__header {
        table-layout: auto;
      }
    }

    .rulestate {
      ::v-deep {
        .el-input__inner {
          padding: 0 12px;
          width: 66px;
          color: #07d4ab;
        }
      }
    }

    .rulestate1 {
      ::v-deep {
        .el-input__inner {
          padding: 0 12px;
          width: 66px;
          color: #ff5151;
        }
      }
    }
  }

  .toolbar-box {
    // position: absolute;
    // top: 330px;
    // right: 18px;

    .el-button {
      height: 27px;
      padding: 0 10px;
      // color: #0f0f13;
      // background: #ffffff;
      // border: 1px solid #f2f3f7;
      box-sizing: border-box;
    }
  }

  ::v-deep .el-dialog__wrapper.upload-diglog {
    .el-dialog {
      width: 613px;
      height: 313px;
      box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.08);
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .el-dialog__header {
        width: 100%;
        height: 20px;
        padding: 12px 16px 0 16px;

        > div {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;

          > div:nth-of-type(1) {
            color: #0f0f13;
            font-size: 14px;
            margin-right: 10px;
          }

          > div:nth-of-type(2) {
            color: #116ef9;
            margin-right: auto;
            font-size: 14px;
            cursor: pointer;
          }

          > div:nth-of-type(3) {
            font-size: 14px;
            color: #2c2c35;
          }
        }
      }

      .el-dialog__body {
        padding: 0;
        display: flex;
        justify-content: center;

        .upload-demo {
          width: 517px;
          height: 192px;
          margin: 0;
          display: flex;
          justify-content: center;

          .upload-file-icon {
            width: 69px;
            height: 45px;
            margin-top: 50px;
          }

          .el-upload.el-upload--text {
            width: 100%;
            height: 100%;

            .el-upload-dragger {
              width: 100%;
              height: 100%;
              margin: 0;

              .el-upload__text {
                color: #0f0f13;

                em {
                  color: #116ef9;
                }
              }

              .text-maxsize {
                font-size: 10px;
                margin-top: 7px;
                color: #0f0f13;
              }
            }
          }
        }

        .upload-timing {
          .uploading-box {
            width: 517px;
            height: 76px;
            border: 1px solid #f2f3f7;
            box-sizing: border-box;
            border-radius: 8px;
            padding: 0 24px;
            padding-bottom: 16px;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            position: relative;

            .el-progress-bar__outer {
              height: 6px !important;
            }

            > div:nth-of-type(1) {
              font-size: 14px;
              margin-bottom: 4px;
              padding-right: 50px;
              box-sizing: border-box;

              > span:nth-of-type(1) {
                color: #116ef9;
              }

              > span:nth-of-type(2) {
                color: #0f0f13;
              }
            }

            .success-icon {
              width: 38px;
              height: 38px;
              font-size: 38px;
              position: absolute;
              top: 8px;
              right: 8px;
            }
          }

          > div:nth-of-type(2) {
            margin-top: 8px;
            font-size: 10px;
          }
        }
      }

      .el-dialog__footer {
        padding: 0 16px 16px 16px;

        .cancel {
          width: 78px;
          height: 32px;
          background: #ffffff;
          border: 1px solid #f2f3f7;
          box-sizing: border-box;
          border-radius: 4px;
          color: #0f0f13;
          padding: 0;
        }

        .submit {
          width: 78px;
          height: 32px;
          background: #116ef9;
          border-radius: 4px;
          color: #ffffff;
          padding: 0;
        }

        .submit-d {
          width: 78px;
          height: 32px;
          background: #cecece;
          border-radius: 4px;
          color: #ffffff;
          padding: 0;
          border: 0;
          // cursor: not-allowed;
          pointer-events: none;
        }
      }
    }
  }
}
</style>
<template>
  <div class="searchBar">
    <el-form :model="formData">
      <div v-if="isVisible" class="search">
        <!-- 查询条件 -->
        <div class="search-box">
          <div class="search-box-l">
            <div class="search-box-l-task">
              <span style="width: 42px">任务：</span>
              <el-select
                v-model="formData.task_id"
                multiple
                style="margin-left: 20px"
                filterable
                collapse-tags
                placeholder="请选择"
                @remove-tag="removeTag"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <el-checkbox
                    v-model="item.check"
                    style="display: block"
                    @change="isChecked(item)"
                  >
                    {{ item.label }}
                  </el-checkbox>
                </el-option>
              </el-select>
            </div>
            <div class="search-box-l-timerange">
              <span style="width: 70px">时间范围：</span>
              <div class="block">
                <el-date-picker
                  v-model="formData.time_range"
                  type="datetimerange"
                  :picker-options="pickerOptions"
                  range-separator="~"
                  start-placeholder="选择开始日期时间"
                  end-placeholder="选择结束日期时间"
                  align="right"
                  popper-class="timebox"
                  @change="affirmTime"
                >
                </el-date-picker>
              </div>
            </div>
            <!-- 折线图打开按钮 -->
            <div class="brokenLine" @click="opneLine">
              <span class="activestyle" style="margin-right: 7px; width: 56px"
                >会话时序</span
              >
              <svg-icon v-if="activeline" icon-class="menu-up" fill="#116ef9" />
              <template v-if="!activeline">
                <svg-icon icon-class="menu-down" fill="#116ef9" />
              </template>
            </div>
          </div>
          <div class="search-box-r">
            <el-button plain @click="resetButtonClick1(true)"> 重置 </el-button>
            <el-button plain @click="recordVisible = true">
              查询历史
            </el-button>
            <el-button
              style="background: #07d4ab; color: #fff; border-color: #07d4ab"
              class="add"
              @click="openDiglog(false)"
            >
              添加条件
            </el-button>
            <el-button type="primary" class="seach" @click="debounce">
              <i class="el-icon-search"></i>
              检索
            </el-button>
          </div>
        </div>
        <!-- 会话时许 -->
        <article v-if="activeline" class="search-echarts">
          <div class="search-echarts-head">
            <div class="search-echarts-head-title">会话连接数</div>
            <div
              class="search-echarts-head-toold"
              style="position: absolute; right: 95px"
            >
              <div class="texthover" style="width: 60px">
                <svg-icon
                  icon-class="line-icon1"
                  class="icon-seleted"
                  style="font-size: 30px; cursor: pointer"
                  @click="backtime"
                />
                <div class="texthover-text" style="font-size: 10px">
                  返回上步
                </div>
              </div>
              <div class="texthover" style="width: 60px">
                <svg-icon
                  icon-class="line-icon2"
                  class="icon-seleted1"
                  style="font-size: 30px; cursor: pointer"
                  @click="initialtime"
                />
                <div class="texthover-text" style="font-size: 10px">
                  重置数据
                </div>
              </div>
            </div>
          </div>
          <div class="search-echarts-foot">
            <v-chart
              ref="chart"
              :option="taskline_options"
              autoresize
              :update-options="{ notMerge: true }"
            >
            </v-chart>
          </div>
        </article>
      </div>

      <div class="dialogbox">
        <!-- 检索条件 -->
        <div v-if="isVisible" class="session-query">
          <div class="session-query-tag">
            <el-tag
              v-for="(tag, index) in sessionTags"
              :key="index"
              :class="!tag.icon ? 'sessionTagspause' : 'sessionTags'"
              size
            >
              <div class="sessionTags1">
                <span>
                  <svg-icon v-if="!tag.icon" icon-class="pauseicon" />{{
                    tag.name
                  }}
                  <span v-if="tag.icon">
                    <svg-icon v-show="tag.choose" icon-class="fanXuan" />
                  </span>
                </span>
                <div style="width: 300px">
                  <i
                    style="cursor: pointer"
                    class="el-icon-edit-outline"
                    @click="openDiglog(tag, index)"
                  >
                    修改
                  </i>
                  <i
                    v-if="!tag.icon"
                    style="cursor: pointer"
                    class="el-icon-video-play"
                    @click="handleClose2(tag, index)"
                  >
                    启动</i
                  >
                  <i
                    v-if="tag.icon"
                    style="cursor: pointer"
                    class="el-icon-video-pause"
                    @click="handleClose1(tag, index)"
                  >
                    暂停</i
                  >
                  <i
                    style="cursor: pointer"
                    class="el-icon-circle-close"
                    @click="HANDLE_CLOSE(tag, index)"
                    >取消</i
                  >
                </div>
              </div>
            </el-tag>
          </div>
          <div class="session-query-add">
            <el-button
              v-if="sessionTags.length"
              type="text"
              @click="handleAddTemplate"
            >
              添加至查询模板
            </el-button>
          </div>
        </div>
        <!-- 检索-弹窗 -->
        <el-dialog
          v-dialogDrag
          :visible.sync="centerDialogVisible"
          center
          :modal="false"
          :close-on-click-modal="false"
        >
          <div class="tab-box">
            <el-tabs v-model="activeName">
              <el-tab-pane v-if="!targetdis" label="多目标检索" name="target">
                <div class="tab-box-top">
                  <span class="title">目标类型</span>
                  <el-form-item style="position: relative">
                    <el-radio-group
                      v-model="formData.target_filter.type"
                      @change="resetChoose"
                    >
                      <el-radio :label="8">会话</el-radio>
                      <el-radio :label="1">IP</el-radio>
                      <el-radio :label="2">域名</el-radio>
                      <el-radio :label="3">指纹</el-radio>
                      <el-radio :label="4">证书</el-radio>
                      <el-radio :label="6">标签</el-radio>
                    </el-radio-group>
                    <span
                      v-if="formData.target_filter.type == '6'"
                      class="detail"
                      @click="handleTagDialog"
                      >标签库</span
                    >
                  </el-form-item>
                </div>
                <div class="tab-box-mid">
                  <span class="title">目标内容</span>
                  <el-form-item v-if="radio != '2'">
                    <el-input
                      v-if="radio != '6'"
                      v-model="formData.target_filter.val"
                      style="width: 600px; vertical-align: middle"
                      type="textarea"
                      :placeholder="
                        radio == '1'
                          ? '一行一个目标或用逗号分隔，支持IP段的检索，如 a.b.c.d/16 '
                          : '一行一个目标，或用逗号分隔'
                      "
                      size="medium"
                    ></el-input>
                    <!-- 标签框 -->
                    <div v-if="radio == '6'" class="tagbox">
                      <span v-for="item in activetags" :key="item.tag_id">
                        <span class="tag-item">
                          <div style="display: inline-block">
                            <el-tag
                              class="tag"
                              :type="item.tag_level"
                              effect="light"
                              closable
                              @close="tagClose(item)"
                            >
                              <div>{{ item.tag_text }}</div>
                            </el-tag>
                          </div>
                        </span>
                      </span>
                    </div>
                  </el-form-item>
                  <!-- 域名输入框 -->
                  <el-form-item v-if="radio == '2'">
                    <el-input
                      v-model="formData.target_filter.val"
                      style="width: 600px; vertical-align: middle"
                      type="textarea"
                      placeholder="一行一个目标或用逗号分隔，支持模糊检索，以“*”开头，如*qq.com"
                      size="medium"
                    ></el-input>
                  </el-form-item>
                  <el-form-item label="">
                    <el-checkbox v-model="choose">反选</el-checkbox>
                  </el-form-item>
                </div>
              </el-tab-pane>
              <el-tab-pane
                v-if="!quintupledis"
                label="五元组检索"
                name="quintuple"
              >
                <div class="quintuple-top">
                  <div class="quintuple-top-l">
                    <span class="title">客户端IP</span>
                    <el-form-item class="IPArea">
                      <el-input
                        ref="sIPArea"
                        v-model="formData.flow_filter.ip_1"
                        class="textarea"
                        type="textarea"
                        placeholder="一行一个IP，默认无限制"
                        size="medium"
                        style="width: 300px; vertical-align: middle"
                      >
                      </el-input>
                    </el-form-item>
                  </div>
                  <div class="quintuple-top-r">
                    <span class="title">服务器IP</span>
                    <el-form-item class="IPArea">
                      <el-input
                        ref="dIPArea"
                        v-model="formData.flow_filter.ip_2"
                        class="textarea"
                        type="textarea"
                        placeholder="一行一个IP，默认无限制"
                        size="medium"
                        style="width: 300px; vertical-align: middle"
                      >
                      </el-input>
                    </el-form-item>
                  </div>
                </div>
                <div class="quintuple-mid">
                  <div class="quintuple-mid-l">
                    <span class="title">IP协议</span>
                    <el-form-item>
                      <el-select
                        v-model="formData.flow_filter.ip_pro"
                        size="medium"
                        value-key="value"
                        clearable
                        filterable
                        placeholder="请选择"
                        style="width: 300px"
                        :reserve-keyword="false"
                      >
                        <el-option
                          v-for="item in ip_pro_full_options"
                          :key="item.id"
                          :label="item.groupname"
                          :value="item.id"
                        >
                          <span style="float: left">{{
                            item.protocol_remark
                          }}</span>
                          <span style="float: right">
                            {{ item.protocol_type }}
                          </span>
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </div>
                  <div class="quintuple-mid-r">
                    <span class="title">应用</span>
                    <el-form-item>
                      <el-select
                        v-model="formData.flow_filter.app_id"
                        size="medium"
                        value-key="value"
                        clearable
                        filterable
                        placeholder="请选择"
                        style="width: 300px"
                        :reserve-keyword="false"
                      >
                        <el-option
                          v-for="item in app_full_options"
                          :key="item.id"
                          :label="item.groupname"
                          :value="item.id"
                        >
                          <span style="float: left">{{ item.zhName }}</span>
                          <span style="float: right">
                            {{ item.enName }}
                          </span>
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </div>
                </div>
                <div class="quintuple-down">
                  <span class="title">目的IP端口范围</span>
                  <el-form-item>
                    <div style="display: inline">
                      <el-tooltip
                        class="item"
                        effect="dark"
                        content="请输入0~65535的端口号"
                        placement="top"
                      >
                        <el-input-number
                          v-model="formData.flow_filter.dst_port[0]"
                          style="width: 144px"
                          :controls="false"
                          :min="0"
                          :max="formData.flow_filter.dst_port[1] || 65535"
                          class="number-input"
                          size="medium"
                        >
                        </el-input-number>
                      </el-tooltip>
                      <span class="to-line"> ~ </span>
                      <el-tooltip
                        class="item"
                        effect="dark"
                        content="请输入0~65535的端口号"
                        placement="top"
                      >
                        <el-input-number
                          v-model="formData.flow_filter.dst_port[1]"
                          style="width: 144px"
                          :controls="false"
                          :min="formData.flow_filter.dst_port[0] + 1 || 0"
                          :max="65535"
                          class="number-input"
                          size="medium"
                        >
                        </el-input-number>
                      </el-tooltip>
                    </div>
                  </el-form-item>
                </div>
              </el-tab-pane>
              <el-tab-pane
                v-if="!esquery1dis"
                label="ES字段检索"
                name="esquery1"
              >
                <div class="thirdbox">
                  <div class="thirdbox-top">
                    <span class="title">字段选择</span>
                    <el-form-item>
                      <div class="block">
                        <el-cascader
                          ref="cascaderRef"
                          v-model="values"
                          filterable
                          :options="searchFileds"
                          @change="handleChange"
                        ></el-cascader>
                      </div>
                    </el-form-item>
                  </div>
                  <div class="thirdbox-mid">
                    <span class="title">字段内容</span>
                    <el-form-item prop="esinput">
                      <el-input
                        v-model.trim="esinput"
                        placeholder="请输入ES字段检索"
                        style="width: 314px"
                        size="medium"
                        @blur="handleInput"
                      >
                      </el-input>
                      <div v-show="validateIp" style="color: red">
                        请输入正确的IP格式
                      </div>
                    </el-form-item>
                    <el-form-item label="" style="margin-top: 10px">
                      <el-checkbox v-model="choose">反选</el-checkbox>
                    </el-form-item>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
            <div class="tab-box-down">
              <div class="text">
                多级检索最多可支持 <span>20</span> 个检索条件
              </div>
              <div>
                <el-button @click="closediaing">关闭</el-button>
                <el-button @click="resetButtonClick">重置</el-button>

                <el-button
                  v-if="setshow"
                  type="primary"
                  style="color: #fff"
                  @click="searchButtonClick"
                >
                  {{ isVisible ? "检索" : "添加条件" }}
                </el-button>
                <el-button
                  v-if="!setshow"
                  type="primary"
                  style="color: #fff"
                  @click="SetSearchButton"
                >
                  修改
                </el-button>
              </div>
            </div>
          </div>
        </el-dialog>
        <!-- 标签库组件 -->
        <tag-view
          v-model="tagLibVisible"
          :tag-labels="isAdd ? [] : activetags"
          :is-add="isAdd"
          @modifyLabels="SUBMIT_TAGS"
        />
        <!-- 查询历史 -->
        <Record
          v-if="isVisible"
          v-model="recordVisible"
          @searchQuery="searchQuery"
        />
        <!-- 添加查询到模板 -->
        <TemplateRemark
          v-model="visibleRemark"
          :btn-loading="btnLoading"
          @handleConfirm="handleConfirm"
        />
      </div>
    </el-form>
  </div>
</template>
<script>
import Vue from "vue";
import * as echarts from "echarts";
Vue.prototype.$echarts = echarts;
import request from "@/utils/request";
import {
  getTargetKbv,
  getTasklist,
  getESmain,
  getProlist,
} from "@/api/Conversational/conversational";
import Record from "./record";
import * as api from "@/api/record";
import * as constans from "./trafficRetrievalConstant";
import { taskline_options } from "./tablechartsData";
import { dictBook } from "@/api/user";
import moment from "moment";
import { cloneDeep } from "lodash";
import TagView from "@/components/TagView";
let newtime = moment().valueOf();
let lasttime = moment().subtract(24, "hours").valueOf();
export default {
  name: "TrafficRetrieval",
  components: {
    TagView,
    Record,
    TemplateRemark: () => import("./record/TemplateRemark"),
  },
  props: {
    isVisible: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isAdd: true,
      dictObj: {},
      validateIp: false,
      cascaderTypeBool: "string",
      tagLibVisible: false,
      recordVisible: false,
      visibleRemark: false,
      btnLoading: false,
      allTask: [], // 所有任务
      // 控制检索按钮
      btndisabled: false,
      // 储存标签内容
      tagdata: [],
      // 任务流量折线图配置
      taskline_options,
      //检索和修改隐藏设置
      setshow: false,
      // 显示隐藏
      showinput: true,
      // 大小于数组
      judgevalue: "",
      // 标签条件名
      searchTagdata: "",
      // 检索条件中tab的选择字段
      tagactvie: "",
      oldtagactvie: "",
      // tab禁止设置
      targetdis: false,
      quintupledis: false,
      esquery1dis: false,
      value: "",
      activetags: [], //选中的标签值
      pause: false, //暂停标识
      formData: {
        task_id: [],
        time_range: [lasttime, newtime],
        flow_filter: {
          ip_1: "",
          ip_2: "",
          ip_pro: "",
          app_id: "",
          src_port: [0, 65535],
          dst_port: [0, 65535],
        },
        target_filter: {
          type: 8,
          val: "",
          labelArr: [],
        },
      },
      targetType: constans.targetType,
      targetTypeValue: constans.targetTypeValue,
      targetTypeNum: constans.targetTypeNum,
      esoptions: constans.esoptions,
      ip_pro_full_options: [],
      app_full_options: [],
      options: [],
      task_dic: {},
      activeName: "target",
      textarea: "",
      esinput: "",
      display: "none",
      initTime: 0,
      disabledIPMark: false,
      filterList: [],
      fivefilterList: [],
      allfilterList: [],
      esfilterList: [],
      choose: false,
      task_id_initial: [],
      activeNames: ["1"], //手风琴展示哪些部分
      Stime: "", //echarts 选中的开始时间
      Etime: 4129694716,
      sessionTags: [],
      interval: 0,
      arrCharts: [],
      fanwei: true,
      query: [],
      sessappid: [],
      sesstag: [],
      esvalue: [],
      values: [],
      stopindex: [],
      fivestopindex: [],
      esstopindex: [],
      activeline: false, //控制折线图打开关闭
      centerDialogVisible: false,
      opentag: false,
      val: "",
      tagval: "",
      radio: 8,
      textdata: "",
      // 时间选择设置
      pickerOptions: {
        shortcuts: [
          {
            text: "查询1小时",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 1);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "查询3小时",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 3);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "查询1天",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "查询3天",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "查询7天",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "查询1月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "查询3月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "查询半年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 183);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "查询1年",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "查询所有",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 1000);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },

      saveValues: [], // 记录值
      esoptions1: [],
      // 从规则页面传来的值
      urldata: {},
      // 标签的快捷检索
      tagSeachdata: {},
      //存储快速减速标签内容
      tagsearch: {},
      // 存选中的时间
      Timearr: [],
      numdata: 0,
      cascaderLable: "", // 级联选中的值
    };
  },
  computed: {
    edittable() {
      return this.$store.state.conversational.jsonedit;
    },
    taskStatus() {
      return this.$store.state.conversational.taskStatus;
    },
    timeQuickSearch() {
      return this.$store.state.conversational.timeQuickSearchData;
    },
    timeRang() {
      return this.formData.time_range;
    },
    searchsian() {
      return this.$store.state.conversational.requestsign;
    },
    searchFileds() {
      return this.dictObj.es_query_field;
    },
    dict() {
      return this.$store.state.long.Dict;
    },
  },
  watch: {
    "$store.state.conversational.jsonedit": {
      deep: true,
      handler(val) {
        this.esinput = val.amout;
        this.esvalue = val.val;
        this.activeName = "esquery1";
        this.searchButtonClick();
      },
    },
    activeName(val) {
      // this.$emit("tagName", val);
      this.tagactvie = val;
    },
    formData: {
      deep: true,
      handler(val) {
        let arr = this.formData.target_filter.val
          ? this.formData.target_filter.val
              .replace(/\n|\，/g, ",")
              .split(",")
              .reduce((prev, item) => {
                if (item.trim()) prev.push(item.trim());
                return prev;
              }, [])
          : [];
        this.disabledIPMark = arr.length > 1 ? true : false;
      },
    },
    "$store.state.conversational.getQuickSearchData": {
      deep: true,
      handler(val) {
        if (val.sort) {
          // 反向
          this.choose = true;
          this.activeName = "esquery1";
          if (val.sortname == "IP") {
            this.activeName = "target";
            this.formData.target_filter.type = 1;
            this.formData.target_filter.val = val.str || val.ip;
          }
          if (val.sortname == "AllDomain") {
            this.activeName = "target";
            this.formData.target_filter.type = 2;
            this.formData.target_filter.val = val.str || val.domain;
          }
          if (val.sortname == "Cert") {
            this.activeName = "target";
            this.formData.target_filter.type = 4;
            this.formData.target_filter.val = val.str || val.cert;
          }
          if (val.sortname == "Finger") {
            this.activeName = "target";
            this.formData.target_filter.type = 3;
            this.formData.target_filter.val = val.str || val.fingerprint;
          }
          if (val.sortname == "dip") {
            this.esvalue = "dIp";
            this.esinput = val.dst_ip;
            this.saveValues = [val.value, "dIp"];
          }
          if (val.sortname == "sip") {
            this.esvalue = "sIp";
            this.esinput = val.src_ip;
            this.saveValues = [val.value, "sIp"];
          }
          if (val.sortname == "dPort") {
            this.esvalue = "dPort";
            this.esinput = val.dst_port + "";
            this.saveValues = [val.value, "dPort"];
          }
          if (val.sortname == "AppName") {
            this.esvalue = "AppName";
            this.esinput = val.AppName;
            this.saveValues = [val.value, "AppName"];
          }
          if (val.sortname == "IPPro") {
            this.esvalue = "IPPro";
            this.esinput = val.ipProtocol;
          }
          if (val.sortname == "ProName") {
            this.esvalue = "ProName";
            this.esinput = val.ProName;
            this.saveValues = [val.value, "ProName"];
          }
          if (val.sortname == "Domain") {
            this.esvalue = "INDEX_DNS:Domain";
            this.esinput = val.Domain;
            this.saveValues = [val.value, "Domain"];
          }
          if (val.sortname == "dCertHash") {
            this.esvalue = "SSL.dCertHash";
            this.esinput = val.hash;
            this.saveValues = [val.value, "dCertHash"];
          }
          if (val.sortname == "Client.User-Agent") {
            this.esvalue = "INDEX_HTTP:Client.User-Agent";
            this.esinput = val.Client["User-Agent"] || val["Client.User-Agent"];
          }
          if (val.sortname == "Client.Host") {
            this.esvalue = "INDEX_HTTP:Client.Host";
            this.esinput = val.Client.Host;
            this.saveValues = [val.value, "Host"];
          }
          if (val.sortname == "HTTP.Host") {
            this.esvalue = "HTTP.Host";
            this.esinput = val.Host;
            this.saveValues = [val.value, "Host"];
          }
          if (val.sortname == "Url") {
            this.esvalue = "INDEX_HTTP:Url";
            this.esinput = val.Url;
          }
          if (val.sortname == "sSSLFinger") {
            this.esvalue = "sSSLFinger";
            this.esinput = val.sSSLFinger;
            this.saveValues = [val.value, "dSSLFinger"];
          }
          if (val.sortname == "dSSLFinger") {
            this.esvalue = "dSSLFinger";
            this.esinput = val.dSSLFinger;
            this.saveValues = [val.value, "dSSLFinger"];
          }
          if (val.sortname == "SSL.CH_ServerName") {
            this.esvalue = "SSL.CH_ServerName";
            this.esinput = val.CH_ServerName;
            this.saveValues = [val.value, "CH_ServerName"];
          }
          if (val.sortname == "tagSeach") {
            this.activeName = "target";
            this.tagSeachdata = val;
            this.formData.target_filter.type = 6;
            this.formData.target_filter.val = this.tagSeachdata.tag_id + "";
          }
          if (val.sortname == "Labels") {
            this.activeName = "target";
            this.tagSeachdata = val;
            this.formData.target_filter.type = 6;
            this.formData.target_filter.val = this.tagSeachdata.tag_id + "";
            this.formData.target_filter.labelArr = [this.tagSeachdata];
          }
          if (val.sortname == "SessionId") {
            this.activeName = "target";
            this.formData.target_filter.type = 8;
            this.formData.target_filter.val = val.session_id || val.SessionId;
          }
        } else {
          // 正向
          this.choose = false;
          this.activeName = "esquery1";
          if (val.sortname == "IP") {
            this.activeName = "target";
            this.formData.target_filter.type = 1;
            this.formData.target_filter.val = val.str || val.ip;
          }
          if (val.sortname == "AllDomain") {
            this.activeName = "target";
            this.formData.target_filter.type = 2;
            this.formData.target_filter.val = val.str || val.domain;
          }
          if (val.sortname == "Cert") {
            this.activeName = "target";
            this.formData.target_filter.type = 4;
            this.formData.target_filter.val = val.str || val.cert;
          }
          if (val.sortname == "Finger") {
            this.activeName = "target";
            this.formData.target_filter.type = 3;
            this.formData.target_filter.val = val.str || val.fingerprint;
          }
          if (val.sortname == "dip") {
            this.esvalue = "dIp";
            this.esinput = val.dst_ip;
            this.saveValues = [val.value, "dIp"];
          }
          if (val.sortname == "sip") {
            this.esvalue = "sIp";
            this.esinput = val.src_ip;
            this.saveValues = [val.value, "sIp"];
          }
          if (val.sortname == "dPort") {
            this.esvalue = "dPort";
            this.esinput = val.dst_port + "";
            this.saveValues = [val.value, "dPort"];
          }
          if (val.sortname == "AppName") {
            this.esvalue = "AppName";
            this.esinput = val.AppName;
            this.saveValues = [val.value, "AppName"];
          }
          if (val.sortname == "IPPro") {
            this.esvalue = "IPPro";
            this.esinput = val.ipProtocol;
          }
          if (val.sortname == "ProName") {
            this.esvalue = "ProName";
            this.esinput = val.ProName;
            this.saveValues = [val.value, "ProName"];
          }
          if (val.sortname == "Domain") {
            this.esvalue = "INDEX_DNS:Domain";
            this.esinput = val.Domain;
            this.saveValues = [val.value, "Domain"];
          }
          if (val.sortname == "dCertHash") {
            this.esvalue = "SSL.dCertHash";
            this.esinput = val.hash;
            this.saveValues = [val.value, "dCertHash"];
          }
          if (val.sortname == "Client.User-Agent") {
            this.esvalue = "INDEX_HTTP:Client.User-Agent";
            this.esinput = val["Client.User-Agent"] || val.Client["User-Agent"];
          }
          if (val.sortname == "dHTTPFinger") {
            this.esvalue = "INDEX_HTTP:dHTTPFinger";
            this.esinput = val.dHTTPFinger;
          }
          if (val.sortname == "Client.Host") {
            this.esvalue = "INDEX_HTTP:Client.Host";
            this.esinput = val.Client.Host;
            this.saveValues = [val.value, "Host"];
          }
          if (val.sortname == "HTTP.Host") {
            this.esvalue = "HTTP.Host";
            this.esinput = val.Host;
            this.saveValues = [val.value, "Host"];
          }
          if (val.sortname == "Url") {
            this.esvalue = "INDEX_HTTP:Url";
            this.esinput = val.Url;
          }
          if (val.sortname == "sSSLFinger") {
            this.esvalue = "sSSLFinger";
            this.esinput = val.sSSLFinger;
            this.saveValues = [val.value, "dSSLFinger"];
          }
          if (val.sortname == "dSSLFinger") {
            this.esvalue = "dSSLFinger";
            this.esinput = val.dSSLFinger;
            this.saveValues = [val.value, "dSSLFinger"];
          }
          if (val.sortname == "SSL.CH_ServerName") {
            this.esvalue = "SSL.CH_ServerName";
            this.esinput = val.CH_ServerName;
            this.saveValues = [val.value, "CH_ServerName"];
          }
          if (val.sortname == "tagSeach") {
            this.activeName = "target";
            this.tagSeachdata = val;
            this.formData.target_filter.type = 6;
            this.formData.target_filter.val = this.tagSeachdata.tag_id + "";
          }
          if (val.sortname == "Labels") {
            this.tagsearch = val;
            this.activeName = "target";
            this.tagSeachdata = val;
            this.formData.target_filter.type = 6;
            this.formData.target_filter.val = this.tagSeachdata.tag_id + "";
            this.formData.target_filter.labelArr = [this.tagSeachdata];
          }
          if (val.sortname == "SessionId") {
            this.activeName = "target";
            this.formData.target_filter.type = 8;
            this.formData.target_filter.val = val.session_id || val.SessionId;
          }
        }
        this.searchButtonClick();
      },
    },
    timeQuickSearch: {
      deep: true,
      handler(val) {
        this.formData.time_range = val;
        this.searchButtonClick();
      },
    },
  },
  async mounted() {
    await this.EXTERNAL();
    await this.getTask();
    await this.getDicBook();
    this.GETTAGS();
    this.urldata = this.$route.query.searchDataPos;
    this.tagsearchfn();
    let tag = this.dictObj.all_tag_category;
    for (let q in tag) {
      let item = {
        name: tag[q].tag_text,
        index: tag[q].tag_id,
      };
      this.sesstag.push(item);
    }
    this.initTime = this.dict.init_time;
    if (this.initTime != 0) {
      this.formData.time_range[0] =
        new Date().getTime() - 3600 * 24 * this.initTime * 1000;
      this.Stime = parseInt(this.formData.time_range[0] / 1000); //单位 s
      this.Stime1 = this.Stime; //datazoom
      this.formData.time_range[1] = new Date().getTime();
      this.Etime = parseInt(this.formData.time_range[1] / 1000);
      this.Etime1 = this.Etime;
    }
    this.initIPProOptions();
    this.initAppOptions();
    this.initEsOptions();
  },
  methods: {
    // 创建自定义证书检索查询模板
    handleAddTemplate() {
      this.visibleRemark = true;
    },
    // 保存查询模板
    async handleConfirm(template_remark, callback) {
      try {
        this.btnLoading = true;
        this.isEs();
        const params = {
          query: this.query,
          template_remark: template_remark,
          user_id: 1,
          query_cn: this.sessionTags,
        };
        await api.create(params);
        this.btnLoading = false;
        callback(false);
        this.$message.success("操作成功");
      } catch (error) {
        console.log(error, "error");
        this.btnLoading = false;
      }
    },
    // 添加es字段标识
    isEs() {
      let indexs = this.sessionTags
        .filter((item) => item.icon)
        .map((tag, index) => (tag.mold === "esfilter" ? index : -1))
        .filter((index) => index !== -1);
    },
    // 获取数据字典
    async getDicBook() {
      const res = await dictBook();
      this.dictObj = res.data;
    },
    handleInput(e) {
      if (this.cascaderTypeBool) {
        const inputValue = e.target.value;
        const ipRegex = /\b(?:\d{1,3}\.){3}\d{1,3}\b/;
        const isValidIP = ipRegex.test(inputValue);
        this.validateIp = !isValidIP;
      }
    },
    handleChange(val) {
      if (val) {
        this.formatEsvalue(val);
        this.esinput = "";
        this.validateIp = false;
        this.cascaderTypeBool =
          this.$refs.cascaderRef.getCheckedNodes()[0]?.data.type === "IP";
        this.$nextTick(() => {
          this.cascaderLable =
            this.$refs.cascaderRef.getCheckedNodes()[0].label;
        });
        this.saveValues = cloneDeep(val);
      }
    },
    formatEsvalue(val) {
      let type = val[0] === "connectinfo";
      this.esvalue = cloneDeep(
        type ? val.at(-1) : `INDEX_${val[0]}:${val.at(-1)}`
      );
    },
    // 检索功能的防抖方法
    debounce(delay = 500) {
      let timer = null;
      if (timer) {
        clearTimeout(timer);
      }
      timer = setTimeout(() => {
        this.searchButtonClick();
        clearTimeout(timer);
      }, delay);
    },
    // 工作台跳转到会话分析查询数据的方法
    tagsearchfn() {
      if (this.urldata != null) {
        this.$store.state.conversational.taglist01.forEach((item) => {
          if (item.tag_id == this.urldata.rule_id) {
            if (
              item.black_list >= 1 &&
              item.black_list <= 100 &&
              item.white_list !== 100
            ) {
              if (item.black_list >= 80) {
                item.type = "danger";
              } else {
                item.type = "warning";
              }
            }
            if (
              item.white_list >= 1 &&
              item.white_list <= 100 &&
              item.black_list === 0
            ) {
              if (item.white_list === 100) {
                item.type = "success";
              } else {
                item.type = "";
              }
            }
            if (item.white_list === 0 && item.black_list === 0) {
              item.type = "info";
            }
            if (this.urldata && !this.urldata.neg) {
              //正选
              this.tagsearch = item;
              this.activeName = "target";
              this.tagSeachdata = item;
              this.formData.target_filter.type = 6;
              this.formData.target_filter.val = this.tagSeachdata.tag_id + "";
              this.choose = false;
            } else if (this.urldata && this.urldata.neg) {
              // 反选
              this.tagsearch = item;
              this.activeName = "target";
              this.tagSeachdata = item;
              this.formData.target_filter.type = 6;
              this.formData.target_filter.val = this.tagSeachdata.tag_id + "";
              this.choose = true;
            }
          }
        });
        this.searchButtonClick();
      }
    },
    // 外部跳转进入生成快速检索函数
    EXTERNAL() {
      if (!this.$route.query.quickData) return;
      let val = JSON.parse(this.$route.query.quickData);
      this.choose = val.choose;
      this.formData.task_id = val.task_ids; // 任务
      this.formData.time_range = null; // 时间范围
      switch (val.type) {
        case "IP":
          this.activeName = "target";
          this.formData.target_filter.type = 1;
          this.formData.target_filter.val = val.targets[0].name;
          break;
        case "CAUTION":
          this.activeName = "target";
          this.formData.target_filter.type = 1;
          this.formData.target_filter.val = val.ip;
          break;
        default:
          break;
      }
    },
    // 多选框触发
    isChecked(item) {
      if (item.check && this.formData.task_id.indexOf(item.value) == -1) {
        this.formData.task_id.push(item.value);
      } else if (!item.check) {
        this.formData.task_id.forEach((elm, idx) => {
          if (elm == item.value) {
            this.formData.task_id.splice(idx, 1);
          }
        });
      }
      let box = [];
      this.options.forEach((item) => {
        if (item.check) {
          box.push(item);
        }
      });
    },
    // 多选模式下移除tag时触发
    removeTag(value) {
      this.options.forEach((elm, idx) => {
        if (elm.value == value) {
          elm.check = false;
        }
      });
    },
    //标签取消按钮
    HANDLE_CLOSE(tag, index) {
      if (tag.mold === "allfilter") {
        // 过滤掉要删除的数据
        for (let i = 0; i < this.allfilterList.length; i++) {
          if (tag.value === this.allfilterList[i].val.join(",")) {
            this.allfilterList.splice(i, 1);
          }
        }
        for (let i = 0; i < this.filterList.length; i++) {
          if (tag.value === this.filterList[i].val.join(",")) {
            this.filterList.splice(i, 1);
          }
        }
      } else if (tag.mold === "esfilter") {
        for (let i = 0; i < this.esfilterList.length; i++) {
          if (tag.value === this.esfilterList[i].val) {
            this.esfilterList.splice(i, 1);
          }
        }
      } else if (tag.mold === "fivefilter") {
        this.fivefilterList = this.fivefilterList.filter(
          (item) => item.id !== tag.id
        );
      }
      this.urldata = {};
      this.handleClose3(tag, index);
      this.$nextTick(() => {
        this.sessionTags = this.sessionTags.filter(
          (item) => item.id !== tag.id
        );
        if (this.sessionTags.length < 1) {
          this.allfilterList = [];
          this.filterList = [];
          this.esfilterList = [];
          this.fivefilterList = [];
        }
        this.searchButtonClick();
      });
    },
    handleClose3(tag, index) {
      tag.icon = true;
      tag.backgroundcolor = "rgba(106, 193, 68, 1.0)";
      tag.bordercolor = "#6ac144";
      if (
        tag.name.slice(0, 3) === "会话=" ||
        tag.name.slice(0, 3) === "IP=" ||
        tag.name.slice(0, 3) === "域名=" ||
        tag.name.slice(0, 3) === "指纹=" ||
        tag.name.slice(0, 3) === "证书=" ||
        tag.name.slice(0, 3) === "目的IP=" ||
        tag.name.slice(0, 4) === "源IP=" ||
        tag.name.slice(0, 7) === "目的IP端口=" ||
        tag.name.slice(0, 3) === "应用=" ||
        tag.name.slice(0, 3) === "标签=" ||
        tag.name.slice(0, 6) === "DNS域名="
      ) {
        if (this.stopindex.includes(index)) {
          this.stopindex.splice(this.stopindex.indexOf(index), 1);
        }
        this.stopindex = this.stopindex.map((val) => {
          if (val > index) {
            val = val - 1;
          }
          return val;
        });
      }
      if (tag.name.slice(0, 5) == "五元组检索") {
        if (this.filterList) {
          if (this.fivestopindex.includes(index - this.filterList.length)) {
            this.fivestopindex.splice(
              this.fivestopindex.indexOf(index - this.filterList.length),
              1
            );
          }
          this.fivestopindex = this.fivestopindex.map((val) => {
            if (val > index - this.filterList.length) {
              val = val - 1;
            }
            return val;
          });
        } else {
          if (this.fivestopindex.includes(index)) {
            this.fivestopindex.splice(index, 1);
          }
          this.fivestopindex = this.fivestopindex.map((val) => {
            if (val > index) {
              val = val - 1;
            }
            return val;
          });
        }
      }

      if (
        tag.name.slice(0, 5) != "五元组检索=" ||
        tag.name.slice(0, 3) != "会话=" ||
        tag.name.slice(0, 3) != "源IP=" ||
        tag.name.slice(0, 3) != "IP=" ||
        tag.name.slice(0, 3) != "域名=" ||
        tag.name.slice(0, 3) != "指纹=" ||
        tag.name.slice(0, 3) != "证书=" ||
        tag.name.slice(0, 7) != "目的IP端口=" ||
        tag.name.slice(0, 3) != "应用=" ||
        tag.name.slice(0, 3) != "标签=" ||
        tag.name.slice(0, 6) != "DNS域名="
      ) {
        if (this.filterList || this.fivefilterList) {
          if (
            this.esstopindex.includes(
              index - this.filterList.length - this.fivefilterList.length
            )
          ) {
            this.esstopindex.splice(
              this.esstopindex.indexOf(
                index - this.filterList.length - this.fivefilterList.length
              ),
              1
            );
          }
          this.esstopindex = this.esstopindex.map((val) => {
            if (
              val >
              index - this.filterList.length - this.fivefilterList.length
            ) {
              val = val - 1;
            }
            return val;
          });
        } else {
          if (this.esstopindex.includes(index)) {
            this.esstopindex.splice(index, 1);
          }
          this.esstopindex = this.esstopindex.map((val) => {
            if (val > index) {
              val = val - 1;
            }
            return val;
          });
        }
      }
    },
    // 标签暂停
    handleClose1(tag, index) {
      tag.icon = false;
      tag.backgroundcolor = "#b9eecf";
      tag.bordercolor = "#b9eecf";
      if (tag.mold === "allfilter") {
        this.stopindex.push(index);
      }
      // if (
      //   tag.name.match(
      //     /^(会话|IP|域名|指纹|证书|目的IP端口|应用|标签|DNS域名|目的IP)=/
      //   )
      // ) {
      //   this.stopindex.push(index);
      // }
      if (tag.name.slice(0, 5) == "五元组检索") {
        if (this.filterList) {
          this.fivestopindex.push(index - this.filterList.length);
        } else {
          this.fivestopindex.push(index);
        }
      }
      if (
        tag.name.slice(0, 5) != "五元组检索" &&
        tag.name.slice(0, 3) != "会话=" &&
        tag.name.slice(0, 3) != "IP=" &&
        tag.name.slice(0, 3) != "指纹=" &&
        tag.name.slice(0, 3) != "证书=" &&
        tag.name.slice(0, 3) != "目的IP=" &&
        tag.name.slice(0, 7) != "目的IP端口=" &&
        tag.name.slice(0, 3) != "应用=" &&
        tag.name.slice(0, 3) != "标签=" &&
        tag.name.slice(0, 6) != "DNS域名="
      ) {
        if (this.filterList || this.fivefilterList) {
          this.esstopindex.push(
            index - this.filterList.length - this.fivefilterList.length
          );
        } else {
          this.esstopindex.push(index);
        }
      }
      // tag.name.slice(0, 3) != "域名=" &&
      this.searchButtonClick();
    },
    // 标签启动
    handleClose2(tag, index) {
      tag.icon = true;
      tag.backgroundcolor = "#ffead6";
      tag.bordercolor = "#ffead6";
      if (
        tag.name.match(
          /^(会话|IP|域名|指纹|证书|目的IP端口|应用|标签|DNS域名)=/
        )
      ) {
        this.stopindex.splice(this.stopindex.indexOf(index), 1);
      }
      if (tag.name.slice(0, 5) == "五元组检索") {
        if (this.filterList) {
          this.fivestopindex.splice(
            this.fivestopindex.indexOf(index - this.filterList.length),
            1
          );
        } else {
          this.fivestopindex.splice(index, 1);
        }
      }

      if (
        (tag.name.slice(0, 5) != "五元组检索" &&
          tag.name.slice(0, 3) != "会话=" &&
          tag.name.slice(0, 3) != "IP=" &&
          tag.name.slice(0, 3) != "域名=" &&
          tag.name.slice(0, 3) != "指纹=" &&
          tag.name.slice(0, 3) != "证书=" &&
          tag.name.slice(0, 7) != "目的IP端口=") ||
        tag.name.slice(0, 3) != "应用=" ||
        tag.name.slice(0, 3) != "标签=" ||
        tag.name.slice(0, 6) != "DNS域名="
      ) {
        if (this.filterList || this.fivefilterList) {
          this.esstopindex.splice(
            this.esstopindex.indexOf(
              index - this.filterList.length - this.fivefilterList.length
            ),
            1
          );
        } else {
          this.esstopindex.splice(index, 1);
        }
      }
      this.searchButtonClick();
    },
    sessheaders() {
      this.query = [];
      let arrstop = [];
      arrstop = JSON.parse(JSON.stringify([...this.filterList]));
      let stop = {
        choose: true,
        type: "暂停",
        val: ["暂停"],
      };
      if (this.stopindex.length > 0) {
        this.stopindex.forEach((val, index) => {
          arrstop.splice(val, 1, stop);
        });
      }
      arrstop.forEach((val) => {
        if (val.type != "暂停") {
          let chos = val.choose ? "not" : "and";
          let value = [
            {
              target: val.type,
              val: val.val,
            },
          ];

          if (value[0].target === "dPort") {
            for (let i in value[0].val) {
              value[0].val[i] = String(value[0].val[i]);
            }
          }
          this.query.push({ bool_search: chos, search: value });
        }
      });
      let fivestop = [];
      fivestop = JSON.parse(JSON.stringify([...this.fivefilterList]));
      let stop1 = {
        stop: "暂停",
      };
      if (this.fivestopindex.length > 0) {
        this.fivestopindex.forEach((val, index) => {
          fivestop.splice(val, 1, stop1);
        });
      }
      fivestop.forEach((val) => {
        if (val.stop != "暂停") {
          let bool = "and";
          let searh = [];
          if (val.ip_1) {
            searh.push({ target: "sIp", val: val.ip_1 });
          }
          if (val.ip_2) {
            searh.push({ target: "dIp", val: val.ip_2 });
          }
          if (val.app_id) {
            let appname = this.app_full_options.filter(
              (o) => o.id === val.app_id
            )[0]?.enName;
            searh.push({ target: "AppName", val: [String(appname)] });
          }
          if (val.ip_pro || val.ip_pro === 0) {
            searh.push({ target: "IPPro", val: [val.ip_pro] });
          }
          if (val.dst_port[0] != 0 || val.dst_port[1] != 65535) {
            searh.push({
              target: "dPort",
              val: [`${String(val.dst_port[0])}-${String(val.dst_port[1])}`],
            });
          }
          this.query.push({
            bool_search: bool,
            search: searh,
          });
        }
      });
      let esarrstop = [];
      esarrstop = JSON.parse(JSON.stringify([...this.esfilterList]));
      let stop2 = {
        choose: true,
        type: "暂停",
        val: ["暂停"],
      };
      if (this.esstopindex.length > 0) {
        this.esstopindex.forEach((val, index) => {
          esarrstop.splice(val, 1, stop2);
        });
      }
      esarrstop.forEach((val) => {
        if (val.type != "暂停") {
          let choose = val.choose ? "not" : "and";
          let value = [
            {
              target: val.type,
              val: [val.val],
            },
          ];
          if (val.type === "StartTime" && !isNaN(Date.parse(value[0].val[0]))) {
            value[0].val[0] = String(
              new Date(value[0].val[0]).getTime() / 1000
            );
          }
          if (val.type === "EndTime" && !isNaN(Date.parse(value[0].val[0]))) {
            value[0].val[0] = String(
              new Date(value[0].val[0]).getTime() / 1000
            );
          }
          if (
            val.type === "HandleBeginTime" &&
            !isNaN(Date.parse(value[0].val[0]))
          ) {
            value[0].val[0] = String(
              new Date(value[0].val[0]).getTime() / 1000
            );
          }
          if (
            val.type === "HandleEndTime" &&
            !isNaN(Date.parse(value[0].val[0]))
          ) {
            value[0].val[0] = String(
              new Date(value[0].val[0]).getTime() / 1000
            );
          }
          this.query.push({ bool_search: choose, search: value });
        }
      });
    },
    // 标签栏的内容
    sessionarr() {
      this.sessionTags = [];
      // Appid 进行单独的文字映射
      for (let i in this.allfilterList) {
        if (this.allfilterList[i].type === "AppId") {
          for (let l in this.allfilterList[i].val) {
            for (let q in this.sessappid) {
              if (
                Number(this.allfilterList[i].val[l]) ===
                Number(this.sessappid[q].index)
              ) {
                this.allfilterList[i].val[l] = this.sessappid[q].name;
              }
            }
          }
        }
        // 标签名 文字映射
        if (this.allfilterList[i].type === "Labels") {
          for (let l in this.allfilterList[i].val) {
            for (let q in this.sesstag) {
              if (
                Number(this.allfilterList[i].val[l]) ===
                Number(this.sesstag[q].index)
              ) {
                this.allfilterList[i].val[l] = this.sesstag[q].name;
                // this.allfilterList[i].arr = this.sesstag[q];
                // console.log(this.allfilterList);
              }
            }
          }
        }
      }
      this.allfilterList.forEach((val, index) => {
        let type = this.targetTypeValue[val.type];
        let value = val?.val.join(",");
        let backgroundcolor;
        let bordercolor;
        let icon = true;
        if (this.stopindex.length > 0) {
          if (this.stopindex.indexOf(index) != -1) {
            backgroundcolor = "rgb(189, 231, 171)";
            bordercolor = "rgb(189, 231, 171)";
            icon = false;
          } else {
            backgroundcolor = "";
            bordercolor = "";
            icon = true;
          }
        }
        let choose = val.choose;
        this.sessionTags.push({
          num: index,
          type,
          value,
          mold: "allfilter",
          name: type + "=" + value,
          icon: icon,
          choose: choose,
          id: val.id,
          tag_id: val.tag_id,
          key: val.type,
          newValue: val?.val,
          labelArr: val?.labelArr,
        });
      });
      this.fivefilterList.forEach((val, index) => {
        let ip_1 = val.ip_1 ? val.ip_1 : "空";
        let ip_2 = val.ip_2 ? val.ip_2 : "空";
        let ip_pro = "空";
        let app_id = "空";
        // 给ip_pro翻译
        if (val.ip_pro || val.ip_pro === 0) {
          ip_pro = this.ip_pro_full_options.find(
            (item) => item.id === val.ip_pro
          )?.protocol_remark;
        }
        app_id = val.app_id
          ? this.app_full_options.filter((o) => o.id === val.app_id)[0]?.enName
          : "空";
        let dst_port0 = val.dst_port[0];
        let dst_port1 = val.dst_port[1];
        let backgroundcolor;
        let bordercolor;
        let icon = true;
        if (this.fivestopindex.length > 0) {
          if (this.fivestopindex.indexOf(index) != -1) {
            backgroundcolor = "rgb(189, 231, 171)";
            bordercolor = "rgb(189, 231, 171)";
            icon = false;
          } else {
            backgroundcolor = "";
            bordercolor = "";
            icon = true;
          }
        }
        let width =
          (
            "五元组检索" +
            ":" +
            " " +
            "客户端IP" +
            "=" +
            ip_1 +
            " " +
            "服务器IP" +
            "=" +
            ip_2 +
            " " +
            "ip协议" +
            "=" +
            ip_pro +
            " " +
            "应用协议" +
            "=" +
            app_id +
            " " +
            "端口范围" +
            "=" +
            dst_port0 +
            "--" +
            dst_port1
          ).length * 7.05;
        if (width > 800) {
          width = "800px";
        } else {
          width = width + "px";
        }
        this.sessionTags.push({
          num: index,
          mold: "fivefilter",
          ip_1,
          ip_2,
          ip_pro: val.ip_pro,
          app_id,
          dst_port0,
          dst_port1,
          name: `五元组检索：${ip_1 != "空" ? "源IP=" + ip_1 : ""} ${
            ip_2 != "空" ? "目的IP=" + ip_2 : ""
          } ${ip_pro != "空" ? "ip协议=" + ip_pro : ""} ${
            app_id != "空" ? "应用协议=" + app_id : ""
          } ${
            dst_port0 && dst_port1
              ? "端口范围=" + dst_port0 + "--" + dst_port1
              : ""
          }`,
          width: width,
          backgroundcolor: backgroundcolor,
          bordercolor: bordercolor,
          icon: icon,
          id: val.id,
        });
      });
      this.esfilterList.forEach((val, index) => {
        let type1;
        let icon = true;
        for (let i in this.esoptions1) {
          if (val.type === this.esoptions1[i].value) {
            type1 = this.esoptions1[i].label;
          }
          if (val.type === "INDEX_DNS:Domain") {
            type1 = "DNS域名";
          }
        }
        if (this.esstopindex.indexOf(index) != -1) {
          backgroundcolor = "rgb(189, 231, 171)";
          bordercolor = "rgb(189, 231, 171)";
          icon = false;
        } else {
          backgroundcolor = "";
          bordercolor = "";
          icon = true;
        }
        let value = val.val;
        let choose = val.choose;
        let backgroundcolor;
        let bordercolor;
        let label = val.cascaderLable || type1;
        this.sessionTags.push({
          num: index,
          type: value,
          target: val?.type,
          value,
          values: val?.values,
          mold: "esfilter",
          name: label + "=" + value,
          cascaderLable: label,
          backgroundcolor: backgroundcolor,
          bordercolor: bordercolor,
          icon: icon,
          choose: choose,
          id: val.id,
        });
      });
    },
    /**
     * 生成随机id
     */
    generateRandomId() {
      return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, (c) =>
        (
          c ^
          (window.crypto.getRandomValues(new Uint8Array(1))[0] &
            (15 >> (c / 4)))
        ).toString(16)
      );
    },
    // 开始时间改变
    changeStartTime(val) {
      if (val != null) {
        this.Stime = parseInt(val.getTime() / 1000);
      }
    },
    // 结束时间改变
    changeEndTime(val) {
      if (val != null) {
        this.Etime = parseInt(val.getTime() / 1000);
      }
    },
    // 获取任务下拉列表
    getTask() {
      getTasklist().then((res) => {
        if (res.err == 0) {
          localStorage.setItem("TaskList", JSON.stringify(res.data));
          let optionsOnlyTaskId = [];
          res.data.forEach((item) => {
            let label = item.taskName;
            if (this.taskStatus.task_id === item.taskId) {
              label += "（任务正在导入中）";
            }
            this.options.push({
              value: item.taskId,
              label,
              check: false,
            });
            optionsOnlyTaskId.push(item.taskId);
            this.task_dic[item.taskId] = item.taskName;
          });
          if (optionsOnlyTaskId) {
            this.task_id_initial = optionsOnlyTaskId.slice(0, 1);
          }
          if (this.formData.task_id?.length === 0) {
            this.formData.task_id = [this.options[0].value];
            this.options[0].check = true;
          }
          this.$emit("taskDic", this.task_dic);
          this.searchButtonClick();
        } else {
          let taskdata = JSON.parse(localStorage.getItem("TaskList"));
          if (taskdata) {
            let optionsOnlyTaskId = [];
            taskdata.forEach((item) => {
              let label = item.taskName;
              if (this.taskStatus.task_id === item.taskId) {
                label += "（任务正在导入中）";
              }
              this.options.push({
                value: item.taskId,
                label,
              });
              optionsOnlyTaskId.push(item.taskId);
              this.task_dic[item.taskId] = item.taskName;
            });
            if (optionsOnlyTaskId) {
              this.task_id_initial = optionsOnlyTaskId.slice(0, 1);
            }
            if (this.formData.task_id.length === 0) {
              this.formData.task_id = [this.options[0].value];
              this.options[0].check = true;
              this.options[1].check = true;
            }
            this.searchButtonClick();
          }
        }
        this.formData.task_id.forEach((id) => {
          this.options.forEach((item) => {
            if (item.value == id) {
              item.check = true;
            }
          });
        });
      });
    },
    // 获取IP协议列表
    initIPProOptions() {
      for (var key in this.dictObj.protocol_type) {
        let tempMode = {};
        tempMode.id = parseInt(key);
        tempMode.protocol_type = this.dictObj.protocol_type[key].protocol_type;
        tempMode.protocol_remark =
          this.dictObj.protocol_type[key].protocol_remark;
        tempMode.groupname = `${this.dictObj.protocol_type[key].protocol_remark}-${this.dictObj.protocol_type[key].protocol_type}`;
        if (!tempMode.protocol_type) {
          tempMode.protocol_type = tempMode.protocol_remark;
        } else if (!tempMode.protocol_remark) {
          tempMode.protocol_remark = tempMode.protocol_type;
        }
        this.ip_pro_full_options.push(tempMode);
      }
    },
    // 获取到应用协议列表
    initAppOptions() {
      getProlist().then((res) => {
        this.app_full_options = [];
        if (res.err == 0) {
          for (let key in res.data) {
            this.app_full_options.push({
              id: res.data[key].pro_id,
              enName: res.data[key].pro_value,
              zhName: res.data[key].pro_name,
              groupname: `${res.data[key].pro_name}-${res.data[key].pro_value}`,
            });
          }
        }
      });
    },
    // 获取到ES字段内容
    initEsOptions() {
      // 同事获取es字段内容
      getESmain().then((res) => {
        if (res.data.indexOf("[") !== -1 && res.data.indexOf("]") !== -1) {
          let esoptions2 = JSON.parse(res.data);
          let esoptions2conn = esoptions2;
          for (let i in esoptions2conn) {
            if (i === "ssl") {
              for (let a in esoptions2conn[i]) {
                if (esoptions2conn[i][a].analysis == 1) {
                  let ssl = {};
                  ssl.value = `${esoptions2conn[i][a].filedName}`;
                  ssl.label = esoptions2conn[i][a].Name;
                  ssl.groupname = `${esoptions2conn[i][a].Name}-${esoptions2conn[i][a].filedName}`;
                  this.esoptions[1].children.push(ssl);
                  this.esoptions1.push(ssl);
                }
              }
            } else if (i === "dns") {
              for (let b in esoptions2conn[i]) {
                if (esoptions2conn[i][b].analysis == 1) {
                  if (esoptions2conn[i][b].filedName == "DNS.Domain") {
                    esoptions2conn[i][b].filedName = "INDEX_DNS:DNS.Domain";
                  }
                  let dns = {};
                  dns.value = `${esoptions2conn[i][b].filedName}`;
                  dns.label = esoptions2conn[i][b].Name;
                  dns.groupname = `${esoptions2conn[i][b].Name}-${esoptions2conn[i][b].filedName}`;
                  this.esoptions[3].children.push(dns);
                  this.esoptions1.push(dns);
                }
              }
            } else if (i === "http") {
              for (let c in esoptions2conn[i]) {
                if (esoptions2conn[i][c].analysis == 1) {
                  let http = {};
                  http.value = `${esoptions2conn[i][c].filedName}`;
                  http.label = esoptions2conn[i][c].Name;
                  http.groupname = `${esoptions2conn[i][c].Name}-${esoptions2conn[i][c].filedName}`;
                  this.esoptions[2].children.push(http);
                  this.esoptions1.push(http);
                }
              }
            } else if (i === "conn") {
              for (let d in esoptions2conn[i]) {
                if (esoptions2conn[i][d].analysis == 1) {
                  if (esoptions2conn[i][d].Name == "") {
                    esoptions2conn[i][d].Name = esoptions2conn[i][d].filedName;
                  }
                  let session = {};
                  session.value = esoptions2conn[i][d].filedName;
                  session.label = esoptions2conn[i][d].Name;
                  session.groupname = `${esoptions2conn[i][d].Name}-${esoptions2conn[i][d].filedName}`;
                  this.esoptions[0].children.push(session);
                  this.esoptions1.push(session);
                }
              }
            }
          }
        }
      });
    },
    // 初始化数据
    resetValue() {
      this.formData.flow_filter.ip_1 = "";
      this.formData.flow_filter.ip_2 = "";
      this.formData.flow_filter.ip_pro = "";
      this.formData.flow_filter.app_id = "";
      this.formData.target_filter.val = "";
      this.formData.flow_filter.dst_port[1] = 65535;
      this.formData.flow_filter.dst_port[0] = 0;
      this.esvalue = "";
      this.esinput = "";
      this.choose = false;
      this.textarea = "";
      this.activetags = [];
      this.aggr_query = false;
      this.tagval = "";
      this.radio = 8;
      this.values = [];
      this.cascaderLable = "";
      this.activeName = "target";
    },
    // 重置列表
    resetButtonClick() {
      let task_id = this.options[0].task_id;
      this.formData = {
        task_id,
        time_range: [lasttime, newtime],
        flow_filter: {
          ip_1: "",
          ip_2: "",
          ip_pro: "",
          app_id: "",
          src_port: [0, 65535],
          dst_port: [0, 65535],
        },
        target_filter: {
          type: this.radio,
          val: "",
        },
      };
      this.activetags = [];
      this.aggr_query = false;
      this.tagval = "";
      this.choose = false;
      this.esvalue = "";
      this.values = [];
      this.validateIp = false;
      this.esinput = "";
      this.Stime = parseInt(this.formData.time_range[0] / 1000);
      this.Etime = parseInt(this.formData.time_range[1] / 1000);
    },
    // 查询是历史查询
    searchQuery(template_text, query_cn) {
      this.resetButtonClick1(false, template_text, query_cn);
    },
    // 外层重置按钮
    resetButtonClick1(bool, template_text, query_cn) {
      let task_id = this.options[0].value;
      this.options.forEach((item) => {
        if (task_id === item.value) {
          item.check = true;
        } else {
          item.check = false;
        }
      });
      this.formData = {
        task_id: [task_id],
        time_range: bool ? [lasttime, newtime] : this.formData.time_range,
        flow_filter: {
          ip_1: "",
          ip_2: "",
          ip_pro: "",
          app_id: "",
          src_port: [0, 65535],
          dst_port: [0, 65535],
        },
        target_filter: {
          type: 8,
          val: "",
        },
      };
      this.Timearr = [];
      this.resetValue();
      this.textarea = "";
      this.choose = false;
      this.Stime = parseInt(this.formData?.time_range?.[0] / 1000); //单位 s
      this.Etime = parseInt(this.formData?.time_range?.[1] / 1000);
      this.fivefilterList = [];
      this.filterList = [];
      this.esfilterList = [];
      this.allfilterList = [];
      this.sessionTags = [];
      this.stopindex = [];
      this.fivestopindex = [];
      this.esstopindex = [];
      if (bool) {
        this.searchButtonClick();
      } else {
        // 处理时间
        let searchData = {
          time_range: [],
          query_cn: [],
        };
        if (this.formData.time_range != null) {
          searchData.time_range = [
            parseInt(this.formData.time_range[0] / 1000),
            parseInt(this.formData.time_range[1] / 1000),
          ];
        } else {
          searchData.time_range = null;
        }
        // 任务
        let task_id = [...new Set(this.formData.task_id)].length
          ? [...new Set(this.formData.task_id)]
          : this.task_id_initial;
        if (task_id.length) {
          searchData.task_id = task_id;
        }
        let targetType = "";
        // 判断类型
        query_cn.forEach((item, index) => {
          if (!item.icon) {
            this.stopindex.push(index);
          }
          if (item.mold === "allfilter") {
            // 多目标查询
            let filterItem = {
              choose: item.choose,
              icon: item.icon,
              type: item.key,
              val: item.newValue,
              tag_id: item.tag_id,
              id: item.id,
            };
            targetType = filterItem.type;
            if (
              this.allfilterList.length === 0 &&
              filterItem.val.length === 0 &&
              this.esfilterList.length === 0 &&
              this.fivefilterList.length === 0
            ) {
              searchData.query = [];
            } else {
              let isExist = false;
              this.allfilterList.forEach((item) => {
                if (JSON.stringify(item) === JSON.stringify(filterItem)) {
                  isExist = true;
                }
              });
              if (!bool && !isExist && filterItem.val.length !== 0) {
                this.filterList.push(filterItem);
                this.allfilterList.push(filterItem);
              }
            }
          }
          if (item.mold === "fivefilter") {
            // 定义一个空对象，数据放到空对象，循环对象赋值fivefilterList push 展示数组里
            let fivefilterList = {};
            if (item.ip_1 && item.ip_1 !== "空") {
              fivefilterList.ip_1 = item.ip_1;
            }
            if (item.ip_2 && item.ip_2 !== "空") {
              fivefilterList.ip_2 = item.ip_2;
            }
            if (item.ip_pro && item.ip_pro !== "空") {
              fivefilterList.ip_pro = item.ip_pro;
            }
            if (item.app_id && item.app_id !== "空") {
              fivefilterList.app_id = this.app_full_options.find(
                (x) => x.enName === item.app_id
              ).id;
            }
            fivefilterList.dst_port = [item.dst_port0, item.dst_port1];
            fivefilterList.id = item.id;
            fivefilterList.icon = item.icon;
            if (item.dst_port0 === 0 && item.dst_port1 === 65535) {
              this.fanwei = false;
            } else if (item.dst_port0 != 0 && item.dst_port1 != 65535) {
              this.fanwei = true;
            }
            let isfalse = true;
            this.fivefilterList.forEach((val) => {
              if (JSON.stringify(val) === JSON.stringify(fivefilterList)) {
                isfalse = false;
              }
            });
            if (isfalse === true) {
              this.fivefilterList.push(fivefilterList);
            }
          }
          if (item.mold === "esfilter") {
            let filterItem = {
              choose: item.choose,
              icon: item.icon,
              type: item.target,
              val: item.value,
              values: item.values,
              id: item.id,
              cascaderLable: item.cascaderLable,
            };
            let isExist = false;
            this.esfilterList.forEach((item) => {
              if (JSON.stringify(item) === JSON.stringify(filterItem)) {
                isExist = true;
              }
            });
            if (!isExist) {
              this.esfilterList.push(filterItem);
            }
          }
        });
        this.values = [];
        this.query = [];
        this.sessheaders();
        searchData.query = this.query;
        this.sessionarr(); // 标签展示样式
        this.resetValue(); // 清空数据
        // 处理时间
        if (searchData.time_range == null) {
          delete searchData.time_range;
        } else {
          searchData.time_range = {
            left: parseInt(this.formData.time_range[0] / 1000),
            right: this.formData.time_range[1]
              ? parseInt(this.formData.time_range[1] / 1000)
              : null,
          };
        }
        setTimeout(() => {
          let Repeatarr = [];
          for (let i in searchData.query) {
            if (searchData.query[i].search.length != 0) {
              if (searchData.query[i].search[0].target === "Labels") {
                for (let l in searchData.query[i].search[0].val) {
                  this.sesstag.forEach((item, index, arr) => {
                    if (searchData.query[i].search[0].val[l] === item.name) {
                      Repeatarr.push(item);
                      searchData.query[i].search[0].val[l] = String(item.index);
                    }
                  });
                }
              }
            }
            if (searchData.query[i].search.length != 0) {
              if (searchData.query[i].search[0].target === "AppId") {
                for (let l in searchData.query[i].search[0].val) {
                  for (let q in this.sessappid) {
                    if (
                      searchData.query[i].search[0].val[l] ===
                      this.sessappid[q].name
                    ) {
                      searchData.query[i].search[0].val[l] = String(
                        this.sessappid[q].index
                      );
                    }
                  }
                }
              }
            }
          }
          searchData.query_cn = query_cn;
          this.flowHistogram(searchData);
          this.$emit("search", searchData);
          if (searchData.type === "multi") {
            this.$emit("triggerActiveName", targetType);
          }
        }, 800);
      }
    },
    /**
     *  检索
     * @param tags
     * @param sing  修改
     * */
    searchButtonClick(tags = true, sing, bool = false) {
      if (this.isVisible && this.formData.task_id?.length === 0) {
        this.$message({
          message: "提示：查询条件为空时，系统将自动查询最新导入的任务数据",
          type: "warning",
        });
        this.formData.task_id = [this.options[0].value];
        this.options[0].check = true;
        this.searchButtonClick();
      } else {
        // 条件最多20个
        if (this.sessionTags.length <= 20) {
          // 验证ip输入格式是否合法
          if (!this.handleBlur()) {
            return;
          }
          // 处理时间
          let searchData = {
            time_range: [],
            query_cn: [],
          };
          if (this.formData.time_range != null) {
            searchData.time_range = [
              parseInt(this.formData.time_range[0] / 1000),
              parseInt(this.formData.time_range[1] / 1000),
            ];
          } else {
            searchData.time_range = null;
          }
          // 任务
          let task_id = [...new Set(this.formData.task_id)].length
            ? [...new Set(this.formData.task_id)]
            : this.task_id_initial;
          if (task_id.length) {
            searchData.task_id = task_id;
          }
          let targetType = "";
          // 判断类型
          if (this.activeName == "target") {
            // 多目标查询
            let filterItem = {
              choose: this.choose,
              type: this.targetType[this.formData.target_filter.type],
              val: this.formData.target_filter.val
                ? this.formData.target_filter.val
                    .replace(/\"/g, "")
                    .replace(/\n|\，/g, ",")
                    .split(",")
                    .reduce((prev, item) => {
                      if (item.trim()) prev.push(item.trim());
                      return prev;
                    }, [])
                : [],
              tag_id: this.tagSeachdata.tag_id,
              id: this.generateRandomId(),
              labelArr: this.formData.target_filter.labelArr || [],
            };
            targetType = filterItem.type;
            if (
              this.allfilterList.length === 0 &&
              filterItem.val.length === 0 &&
              this.esfilterList.length === 0 &&
              this.fivefilterList.length === 0
            ) {
              searchData.query = [];
            } else {
              let isExist = false;
              this.allfilterList.forEach((item) => {
                if (JSON.stringify(item) === JSON.stringify(filterItem)) {
                  isExist = true;
                }
              });
              if (!bool && !isExist && filterItem.val.length !== 0) {
                this.filterList.push(filterItem);
                this.allfilterList.push(filterItem);
              }
              this.query = [];
              // 标签展示
              this.sessheaders();
              searchData.query = this.query;
            }
            this.sessionarr();
            this.resetValue();
            this.$nextTick(() => {
              var scroll =
                this.$refs.scrollbar && this.$refs.scrollbar.$refs.wrap;
              if (scroll) {
                scroll.scrollTop = scroll.scrollHeight - scroll.clientHeight;
              }
            });
          } else if (this.activeName == "quintuple") {
            // 五元查询
            if (sing != "xiugai") {
              let item1 = {};
              if (this.formData.flow_filter.ip_1) {
                item1.ip_1 = this.formData.flow_filter.ip_1
                  .split(/[\n,]+/)
                  .filter((item) => item.trim());
              }
              if (this.formData.flow_filter.ip_2) {
                item1.ip_2 = this.formData.flow_filter.ip_2
                  .split(/[\n,]+/)
                  .filter((item) => item.trim());
              }
              item1.ip_pro = this.formData.flow_filter.ip_pro;
              this.formData.flow_filter.app_id
                ? (item1.app_id = +this.formData.flow_filter.app_id)
                : null;
              item1.src_port = this.formatPortRange(
                this.formData.flow_filter.src_port
              );
              item1.dst_port = this.formatPortRange(
                this.formData.flow_filter.dst_port
              );
              if (
                this.formData.flow_filter.ip_1 ||
                this.formData.flow_filter.ip_2 ||
                this.formData.flow_filter.ip_pro ||
                this.formData.flow_filter.ip_pro === 0 ||
                this.formData.flow_filter.app_id ||
                this.formData.flow_filter.dst_port[0] != 0 ||
                this.formData.flow_filter.dst_port[1] != 65535
              ) {
                if (
                  this.formData.flow_filter.dst_port[0] === 0 &&
                  this.formData.flow_filter.dst_port[1] === 65535
                ) {
                  this.fanwei = false;
                } else if (
                  this.formData.flow_filter.dst_port[0] != 0 &&
                  this.formData.flow_filter.dst_port[1] != 65535
                ) {
                  this.fanwei = true;
                }
                // 定义一个空对象，数据放到空对象，循环对象赋值fivefilterList push 展示数组里
                let fivefilterList = {};
                if (this.formData.flow_filter.ip_1) {
                  fivefilterList.ip_1 = this.formData.flow_filter.ip_1
                    .split(/[\n,]+/)
                    .filter((item) => item.trim());
                }
                if (this.formData.flow_filter.ip_2) {
                  fivefilterList.ip_2 = this.formData.flow_filter.ip_2
                    .split(/[\n,]+/)
                    .filter((item) => item.trim());
                }
                fivefilterList.ip_pro = this.formData.flow_filter.ip_pro;
                fivefilterList.app_id = this.formData.flow_filter.app_id
                  ? +this.formData.flow_filter.app_id
                  : null;
                fivefilterList.src_port = this.formatPortRange(
                  this.formData.flow_filter.src_port
                );
                fivefilterList.dst_port = this.formatPortRange(
                  this.formData.flow_filter.dst_port
                );
                fivefilterList.id = this.generateRandomId();
                let isfalse = true;
                this.fivefilterList.forEach((val) => {
                  if (JSON.stringify(val) === JSON.stringify(fivefilterList)) {
                    isfalse = false;
                  }
                });
                if (isfalse === true) {
                  this.fivefilterList.push(fivefilterList);
                }
                this.query = [];
                this.sessheaders();
                searchData.query = this.query;
                this.sessionarr();
              } else {
                this.query = [];
                this.sessheaders();
                searchData.query = this.query;
                this.sessionarr();
                this.fanwei = true;
              }
            }
            this.query = [];
            this.sessheaders();
            searchData.query = this.query;
            this.sessionarr();
            this.resetValue();
          } else if (this.activeName == "esquery1") {
            if (this.centerDialogVisible) {
              if (!this.values.length) {
                this.$message.warning("请选择字段");
                return;
              }
              if (!this.esinput) {
                this.$message.warning("请输入ES字段检索");
                return;
              }
            }
            if (sing != "es") {
              let filterItem = {
                choose: this.choose,
                type: this.esvalue.length > 0 ? this.esvalue : "",
                val: this.esinput ? this.esinput.replace(/^\s+|\s+$/g, "") : [],
                values: this.saveValues,
                cascaderLable: this.cascaderLable,
                id: this.generateRandomId(),
              };
              if (
                this.esvalue.length === 0 &&
                !this.esinput &&
                this.esfilterList.length === 0 &&
                this.fivefilterList.length === 0 &&
                this.filterList.length === 0
              ) {
                searchData.query = [];
              } else {
                if (this.esvalue.length > 0 && this.esinput) {
                  let isExist = false;
                  this.esfilterList.forEach((item) => {
                    if (JSON.stringify(item) === JSON.stringify(filterItem)) {
                      isExist = true;
                    }
                  });
                  if (!isExist) {
                    this.esfilterList.push(filterItem);
                  }
                }
                this.query = [];
                this.values = [];
                this.sessheaders();
                searchData.query = this.query;
                this.sessionarr();
                this.resetValue();
              }
            }
            this.query = [];
            this.sessheaders();
            searchData.query = this.query;
            this.sessionarr(); // 标签展示样式
            this.resetValue(); // 清空数据
          }
          // 浏览器存储
          if (this.isVisible && searchData.task_id) {
            window.localStorage.setItem(
              "session_task_id",
              JSON.stringify(searchData.task_id)
            );
          }
          // 处理时间
          if (searchData.time_range == null) {
            delete searchData.time_range;
          } else {
            searchData.time_range = {
              left: parseInt(this.formData.time_range[0] / 1000),
              right: this.formData.time_range[1]
                ? parseInt(this.formData.time_range[1] / 1000)
                : null,
            };
          }
          setTimeout(() => {
            let Repeatarr = [];
            for (let i in searchData.query) {
              if (searchData.query[i].search.length != 0) {
                if (searchData.query[i].search[0].target === "Labels") {
                  for (let l in searchData.query[i].search[0].val) {
                    this.sesstag.forEach((item, index, arr) => {
                      if (searchData.query[i].search[0].val[l] === item.name) {
                        Repeatarr.push(item);
                        searchData.query[i].search[0].val[l] = String(
                          item.index
                        );
                      }
                    });
                  }
                }
              }
              if (searchData.query[i].search.length != 0) {
                if (searchData.query[i].search[0].target === "AppId") {
                  for (let l in searchData.query[i].search[0].val) {
                    for (let q in this.sessappid) {
                      if (
                        searchData.query[i].search[0].val[l] ===
                        this.sessappid[q].name
                      ) {
                        searchData.query[i].search[0].val[l] = String(
                          this.sessappid[q].index
                        );
                      }
                    }
                  }
                }
              }
            }
            searchData.query_cn = this.sessionTags;
            if (this.isVisible) {
              this.flowHistogram(searchData);
              this.$emit("search", searchData);
            } else {
              this.$emit("sessionTags", this.sessionTags, searchData);
            }
          }, 800);
          if (searchData.type === "multi") {
            this.$emit("triggerActiveName", targetType);
          }
          this.centerDialogVisible = false;
        } else {
          this.$message.error("快速查询最多查询20条");
        }
      }
    },
    // 修改检索条件
    SetSearchButton() {
      if (this.searchTagdata.mold === "allfilter") {
        if (!this.handleBlur()) {
          return;
        }
        this.activeName = "target";
        this.allfilterList.forEach((item, index) => {
          if (index == this.searchTagdata.num) {
            if (this.formData.target_filter.type == "Domain") {
              item.choose = this.choose;
              item.type = "Domain";
              item.val = this.textarea
                ? this.textarea
                    .replace(/\"/g, "")
                    .replace(/\n|\，/g, ",")
                    .split(",")
                    .reduce((prev, item) => {
                      if (item.trim()) prev.push(item.trim());
                      return prev;
                    }, [])
                : [];
            } else {
              this.targetTypeNum.forEach((itemson) => {
                if (itemson.value == this.formData.target_filter.type) {
                  item.type = itemson.name;
                  item.choose = this.choose;
                }
              });
              item.val = this.formData.target_filter.val
                ? this.formData.target_filter.val
                    .replace(/\"/g, "")
                    .replace(/\n|\，/g, ",")
                    .split(",")
                    .reduce((prev, item) => {
                      if (item.trim()) prev.push(item.trim());
                      return prev;
                    }, [])
                : [];
              item.labelArr = this.formData.target_filter.labelArr || [];
            }
          }
        });
        this.searchButtonClick(true, undefined, true);
      }
      if (this.tagactvie === "quintuple") {
        this.activeName = "quintuple";
        this.fivefilterList.forEach((item, index) => {
          if (index == this.searchTagdata.num) {
            item.choose = this.choose;
            item.app_id = this.formData.flow_filter.app_id
              ? (item.app_id = +this.formData.flow_filter.app_id)
              : null;
            if (this.formData.flow_filter.ip_1.indexOf("\n") >= 0) {
              item.ip_1 = this.formData.flow_filter.ip_1
                ? (item.ip_1 = this.formData.flow_filter.ip_1
                    .split("\n")
                    .filter((item) => item.trim()))
                : null;
            } else {
              if (this.formData.flow_filter.ip_1 != "") {
                item.ip_1 = [this.formData.flow_filter.ip_1];
              } else {
                item.ip_1 = this.formData.flow_filter.ip_1;
              }
            }
            if (this.formData.flow_filter.ip_2.indexOf("\n") >= 0) {
              // alert(aaa + " 中有\n");
              item.ip_2 = this.formData.flow_filter.ip_2
                ? (item.ip_2 = this.formData.flow_filter.ip_2
                    .split("\n")
                    .filter((item) => item.trim()))
                : null;
            } else {
              if (this.formData.flow_filter.ip_2 != "") {
                item.ip_2 = [this.formData.flow_filter.ip_2];
              } else {
                item.ip_2 = this.formData.flow_filter.ip_2;
              }
            }
            item.ip_pro = this.formData.flow_filter.ip_pro;
            item.dst_port[0] =
              this.formData.flow_filter.dst_port[0] == ""
                ? 0
                : this.formData.flow_filter.dst_port[0];
            item.dst_port[1] =
              this.formData.flow_filter.dst_port[1] == ""
                ? 65535
                : this.formData.flow_filter.dst_port[1];
          }
        });
        this.searchButtonClick(true, "xiugai");
      }
      if (this.tagactvie === "esquery1") {
        this.esfilterList.forEach((item, index) => {
          if (index === this.searchTagdata.num) {
            if (this.esvalue == "sPort") {
              if (this.esinput.indexOf("-") != 0) {
                item.choose = this.choose;
                if (this.judgevalue == "小于") {
                  item.val = "-" + this.esinput;
                } else if (this.judgevalue == "大于") {
                  item.val = this.esinput + "-";
                } else {
                  item.val = this.esinput;
                }
              } else {
                item.choose = this.choose;
                item.type = this.esvalue;
                item.val = this.esinput;
              }
            } else {
              item.choose = this.choose;
              item.type = this.esvalue ? this.esvalue : item.type;
              item.val = this.esinput ? this.esinput : item.val;
              item.values = this.values ? this.values : item.values;
              item.cascaderLable = this.cascaderLable
                ? this.cascaderLable
                : item.cascaderLable;
            }
          }
        });
        this.searchButtonClick(true, "es");
      }
    },
    flowHistogram(param) {
      param.segment_num = 7;
      param.aggr_query = false;
      for (let i of param.query) {
        for (let j of i.search) {
          if (j.target === "Labels") {
            param.aggr_query = true;
          }
        }
      }
      if (parseInt(this.Etime - this.Stime) < 0) {
        this.$message.error("开始时间不能晚于结束时间");
      } else {
        if (JSON.stringify(param).length === 12) {
          this.activeNames = ["1"];
        } else {
          // 会发分析流量图
          getTargetKbv(param).then((res) => {
            if (res.err === 0) {
              this.activeNames = ["1", "2"];
              let middle_time = [];
              let starttime = "";
              let cntdata = [];
              let endtime = "";
              for (let i in res.data) {
                if (i == 0) {
                  let timedata1 = moment
                    .unix(res.data[i].start_time)
                    .format("YYYY-MM-DD HH:mm:ss");
                  starttime = timedata1;
                }
                if (i == 6) {
                  let timedata2 = moment
                    .unix(res.data[i].end_time)
                    .format("YYYY-MM-DD HH:mm:ss");
                  endtime = timedata2;
                }
                cntdata.push(res.data[i].cnt);
                // 索引等于0和6就不加入数组
                if (i == 0 || i == 6) {
                  continue;
                } else {
                  middle_time.push(res.data[i].middle_key);
                }
              }
              middle_time.unshift(starttime);
              middle_time.push(endtime);
              this.taskline_options.xAxis.data = middle_time;
              this.taskline_options.series[0].data = cntdata;
            }
          });
        }
      }
    },
    resetChoose(e) {
      this.radio = e;
      this.choose = false; //恢复反选
      this.formData.target_filter.val = "";
    },
    // 选择标签
    SUBMIT_TAGS(tags = [], selectTags, callback = () => {}) {
      this.activetags = JSON.parse(JSON.stringify(selectTags));
      this.tagdata = selectTags;
      let tagName = this.activetags.map((item) => item.tag_text).join(",");
      this.formData.target_filter.val = tagName;
      this.formData.target_filter.labelArr = selectTags; // 把标签集合数据存一份
      callback(true);
    },
    // 标签删除
    tagClose(tag) {
      this.activetags.splice(this.activetags.indexOf(tag), 1);
      let tagName = this.activetags.map((item) => item.tag_text).join(",");
      this.tagval = tagName;
      this.formData.target_filter.val = tagName;
    },
    // 检查服务器端口范围值
    formatPortRange(arr) {
      let portRange = arr.slice(0);
      if (typeof portRange[0] !== "number") portRange[0] = 0;
      if (typeof portRange[1] !== "number") portRange[1] = 65535;
      return portRange;
    },
    openDiglog(tag, index) {
      this.formData.target_filter.type = 8;
      this.centerDialogVisible = true;
      this.isAdd = false;
      if (tag) {
        this.searchTagdata = tag;
        let tagname = "";
        let tablaber = tag.name.split("=")[0];
        for (let i in this.esoptions1) {
          if (tablaber === this.esoptions1[i].label) {
            tagname = this.esoptions1[i].value;
          }
        }
        this.setshow = false;
        if (tag.mold == "allfilter") {
          this.choose = tag.choose;
          this.activeName = "target";
          this.targetdis = false;
          this.quintupledis = true;
          this.esquery1dis = true;
          this.formData.target_filter.val = tag.value;
          // this.activetags = this.tagdata;
          this.targetTypeNum.forEach((item) => {
            if (item.zwname == tablaber) {
              this.allfilterList.forEach((itemson) => {
                if (item.name == itemson.type) {
                  this.formData.target_filter.type = item.value;
                  this.radio = JSON.stringify(item.value);
                }
              });
            }
          });
          if (tag.type == "标签") {
            if (this.tagsearch.tag_text == tag.value) {
              this.tagsearch.tagText = this.tagsearch.tag_text;
              this.SUBMIT_TAGS([], tag.labelArr);
            } else {
              tag.tag_text = tag.value;
              this.SUBMIT_TAGS([], tag.labelArr);
            }
          }
        }
        if (tag.mold == "fivefilter") {
          if (typeof tag.ip_1 !== "string") {
            let tagip_1 = tag.ip_1.map((item) => item).join("\n");
            this.formData.flow_filter.ip_1 = tag.ip_1 == "空" ? "" : tagip_1;
            tagip_1 = [];
          } else {
            this.formData.flow_filter.ip_1 = tag.ip_1 == "空" ? "" : tag.ip_1;
          }

          if (typeof tag.ip_2 !== "string") {
            let tagip_2 = tag.ip_2.map((item) => item).join("\n");
            this.formData.flow_filter.ip_2 = tag.ip_2 == "空" ? "" : tagip_2;
            tagip_2 = [];
          } else {
            this.formData.flow_filter.ip_2 = tag.ip_2 == "空" ? "" : tag.ip_2;
          }
          this.choose = tag.choose;
          this.activeName = "quintuple";
          this.targetdis = true;
          this.quintupledis = false;
          this.esquery1dis = true;
          this.formData.flow_filter.ip_pro =
            tag.ip_pro == "空" ? "" : tag.ip_pro;
          this.formData.flow_filter.app_id =
            tag.app_id == "空" ? "" : tag.app_id;
          this.formData.flow_filter.dst_port[0] = tag.dst_port0;
          this.formData.flow_filter.dst_port[1] = tag.dst_port1;
        }
        if (tag.mold == "esfilter") {
          this.activeName = "esquery1";
          this.targetdis = true;
          this.quintupledis = true;
          this.esquery1dis = false;
          this.formatEsvalue(tag.values);
          this.values = tag.values;
          this.esinput = tag.value.replace(
            /[`~!@#$%^&*()\-+=<>?"{}|,\;'\\[\]~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、]/im,
            ""
          );
          this.choose = tag.choose;
        }
      } else {
        this.setshow = true;
        this.targetdis = false;
        this.quintupledis = false;
        this.esquery1dis = false;
        this.values = []; // 点击条件需要清空字段选择的内容
      }
    },
    // 打开标签弹窗
    opentagdialog() {
      this.opentag = true;
      this.tagval = "";
    },
    cleartagdialog() {
      this.opentag = false;
    },
    handleOpened() {
      // 获取到id返回新的数组
      let tagText = this.activetags.map((item) => {
        return item.tagText;
      });
      this.tagval = tagText.join(",");
    },
    // 打开折线图
    opneLine() {
      this.activeline = !this.activeline;
      // 这里使用chart1.value的形式获取到dom节点
      if (this.activeline) {
        let newPromise = new Promise((resolve) => {
          resolve();
        });
        //然后异步执行echarts的初始化函数
        newPromise.then(() => {
          var myChart = this.$refs.chart.chart;
          myChart.on("brushEnd", (params) => {
            if (this.numdata == 0) {
              this.Timearr.push([lasttime, newtime]);
            }
            let range = params.areas[0].coordRange[0];
            let left = range[0];
            let right = range[1];
            let Time = [
              moment(this.taskline_options.xAxis.data[left]).valueOf(),
              moment(this.taskline_options.xAxis.data[right]).valueOf(),
            ];
            this.Timearr.unshift(Time);
            window.localStorage.setItem("time_range", JSON.stringify(Time));
            this.formData.time_range = this.Timearr[0];
            this.searchButtonClick();
          });
        });
      }
    },
    //  关闭弹窗
    closediaing() {
      this.centerDialogVisible = false;
      this.resetValue();
    },
    // 折线图时间返回按钮
    backtime() {
      if (this.numdata == this.Timearr.length) {
        this.$message.error("最后一条了，请重新框选！");
        this.Timearr = [];
        this.formData.time_range = [lasttime, newtime];
        this.numdata = 0;
      }
      if (!this.Timearr.length) return;
      this.formData.time_range = this.Timearr[this.numdata];
      this.numdata++;
      this.searchButtonClick();
    },
    // 回到初始时间初始值
    initialtime() {
      this.formData.time_range = [lasttime, newtime];
      this.searchButtonClick();
    },
    // 时间确认的回调
    affirmTime(e) {
      this.Timearr = [];
      if (e) {
        let yestime = [moment(e[0]).valueOf(), moment(e[1]).valueOf()];
        this.Timearr.unshift(yestime);
      }
    },
    // 获取标签库
    GETTAGS() {
      request({
        url: "/session/tag/list",
        method: "POST",
      })
        .then((res) => {
          if (res.err == 0) {
            this.$store.commit("conversational/tagseachlist", res.data);
          }
        })
        .catch((err) => {});
    },
    handleTagDialog() {
      this.tagLibVisible = true;
    },
    // 验证是否为ip
    handleBlur() {
      if (this.formData.target_filter.type === 1) {
        let inputValue = this.formData.target_filter.val;
        if (inputValue) {
          const ipRegex =
            /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(?:\/(?:[0-9]|[1-2][0-9]|3[0-2]))?$|^(?:[0-9a-fA-F]{1,4}:){7}(?:[0-9a-fA-F]{1,4}|:)|(?:[0-9a-fA-F]{1,4}:){6}(?:[0-9a-fA-F]{1,4}:)?[0-9a-fA-F]{1,4}|(?:[0-9a-fA-F]{1,4}:){5}(?::[0-9a-fA-F]{1,4}){1,2}|(?:[0-9a-fA-F]{1,4}:){4}(?::[0-9a-fA-F]{1,4}){1,3}|(?:[0-9a-fA-F]{1,4}:){3}(?::[0-9a-fA-F]{1,4}){1,4}|(?:[0-9a-fA-F]{1,4}:){2}(?::[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:(?:(?::[0-9a-fA-F]{1,4}){1,6})|:(?:(?::[0-9a-fA-F]{1,4}){1,7}|:)|::(?:ffff(?::0{1,4}){0,1}:){0,1}(?:(?:25[0-5]|(?:2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3}(?:25[0-5]|(?:2[0-4]|1{0,1}[0-9]){0,1}[0-9])(?:\/(?:[0-9]|[1-2][0-9]|3[0-2]))?$/;
          let inputValueArr = inputValue
            .replace(/\"/g, "")
            .replace(/\n|\，/g, ",")
            .split(",");
          const isValidIP = inputValueArr.every((item) => ipRegex.test(item));
          if (!isValidIP) {
            this.$message.error("IP格式异常，请检查");
            return false;
          } else {
            return true;
          }
        } else {
          return true;
        }
      } else {
        return true;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "./trafficRetrieval.scss";
</style>

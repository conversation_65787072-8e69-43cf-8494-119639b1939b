<template>
  <el-dialog
    title="添加新标签"
    :visible.sync="isShow"
    width="480px"
    append-to-body
    @close="handleClose"
  >
    <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules">
      <el-form-item label="名称" prop="tag_text">
        <el-input
          v-model="ruleForm.tag_text"
          placeholder="请输入标签名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="分类" prop="classification">
        <el-cascader
          v-model="ruleForm.classification"
          style="width: 100%"
          :options="classificationOptions"
          :props="{
            value: 'attribute_id',
            label: 'attribute_name',
          }"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="描述" prop="tag_explain">
        <el-input
          v-model="ruleForm.tag_explain"
          placeholder="请输入标签描述"
          type="textarea"
        ></el-input>
      </el-form-item>
      <div class="flex">
        <el-form-item label="黑名单权重">
          <el-input-number
            v-model="ruleForm.black_list"
            :min="0"
            :max="100"
            :step="1"
            :controls="false"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="白名单权重" prop="delivery">
          <el-input-number
            v-model="ruleForm.white_list"
            :min="0"
            :max="100"
            :step="1"
            :controls="false"
          ></el-input-number>
        </el-form-item>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="isShow = false">取 消</el-button>
      <el-button
        size="small"
        :loading="loading"
        type="primary"
        @click="handleConfirm"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import Mixins from "@/mixins";
import { getTagAdd, getListTagAttribute } from "@/api/TagList";
export default {
  name: "TagAdd",
  mixins: [Mixins],
  data() {
    return {
      ruleForm: {
        tag_text: "", // 标签名称
        tag_explain: "", // 标签描述
        black_list: 0, // 黑名单权值
        white_list: 0, // 白名单权值
        classification: "", // 分类
      },
      classificationOptions: [],
      rules: {
        tag_text: [
          { message: "请输入标签名称", trigger: "blur", required: true },
        ],
        classification: [
          {
            message: "请选择分类",
            trigger: ["blur", "change"],
            required: true,
          },
        ],
      },
      loading: false,
    };
  },
  created() {
    this.getListTagAttribute();
  },
  methods: {
    // 确定
    handleConfirm() {
      this.$refs.ruleFormRef.validate(async (valid) => {
        try {
          if (valid) {
            this.loading = true;
            const params = {
              tag_text: this.ruleForm.tag_text, // 标签名称
              tag_explain: this.ruleForm.tag_explain, // 标签描述
              black_list: this.ruleForm.black_list, // 黑名单权值
              white_list: this.ruleForm.white_list, // 白名单权值
            };
            if (this.ruleForm.classification.length === 2) {
              params.tag_attribute_id = this.ruleForm.classification[1];
              params.tag_target_type = this.ruleForm.classification[0];
            } else {
              params.tag_target_type = this.ruleForm.classification.at(-1);
            }
            await getTagAdd(params);
            this.loading = false;
            this.isShow = false;
            this.$message.success("添加标签成功");
            this.$emit("getList");
          } else {
            this.loading = false;
            return false;
          }
        } catch (error) {
          this.loading = false;
        }
      });
    },
    // 重置
    handleClose() {
      this.$refs.ruleFormRef.resetFields();
      this.ruleForm.black_list = 0;
      this.ruleForm.white_list = 0;
    },
    // 获取标签分类
    async getListTagAttribute() {
      try {
        const res = await getListTagAttribute();
        this.classificationOptions = res.data;
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.flex {
  .el-form-item {
    &:first-child {
      margin-right: 16px;
    }
    ::v-deep {
      .el-input,
      .el-input-number {
        width: 210px;
      }
    }
  }
}
</style>

<template>
  <el-dialog
    title="添加新标签"
    :visible.sync="isShow"
    width="480px"
    append-to-body
    @close="handleClose"
  >
    <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules">
      <el-form-item label="名称" prop="tag_text">
        <el-input
          v-model="ruleForm.tag_text"
          placeholder="请输入标签名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="描述" prop="tag_remark">
        <el-input
          v-model="ruleForm.tag_remark"
          placeholder="请输入标签描述"
          type="textarea"
        ></el-input>
      </el-form-item>
      <div class="flex">
        <el-form-item label="黑名单权重" prop="hmd">
          <el-input-number
            v-model="ruleForm.black_list"
            :min="0"
            :max="100"
            :step="1"
            :controls="false"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="白名单权重" prop="delivery">
          <el-input-number
            v-model="ruleForm.white_list"
            :min="0"
            :max="100"
            :step="1"
            :controls="false"
          ></el-input-number>
        </el-form-item>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="isShow = false">取 消</el-button>
      <el-button :loading="loading" type="primary" @click="handleConfirm"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import Mixins from "@/mixins";
import { addTag } from "@/api/import";
import { analysisTagAdd } from "@/api/TagList";
export default {
  name: "TagAdd",
  mixins: [Mixins],
  props: {
    libaryType: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      ruleForm: {
        tag_text: "", //标签名称
        tag_remark: "", //标签描述
        black_list: 0, //黑名单权值
        white_list: 0, //白名单权值
        attr_type: 8,
      },
      rules: {
        tag_text: [
          { message: "请输入标签名称", trigger: "blur", required: true },
        ],
        tag_remark: [
          { message: "请输入标签描述", trigger: "blur", required: true },
        ],
      },
      loading: false,
    };
  },
  computed: {
    // 证书
    islibary() {
      return this.libaryType === 1;
    },
  },
  methods: {
    // 确定
    handleConfirm() {
      this.$refs.ruleFormRef.validate(async (valid) => {
        try {
          if (valid) {
            this.loading = true;
            if (this.islibary) {
              await addTag({ ...this.ruleForm });
            } else {
              await analysisTagAdd({ ...this.ruleForm });
            }
            this.loading = false;
            this.isShow = false;
            this.$message.success("添加标签成功");
            this.$emit("getList");
          } else {
            this.loading = false;
            return false;
          }
        } catch (error) {
          this.loading = false;
        }
      });
    },
    // 重置
    handleClose() {
      this.ruleForm.tag_text = ""; //标签名称
      this.ruleForm.tag_remark = ""; //标签描述
      this.ruleForm.black_list = 0; //黑名单权值
      this.ruleForm.white_list = 0; //白名单权值
    },
  },
};
</script>

<style lang="scss" scoped>
.el-form {
  padding: 0 22px;
}
.flex {
  .el-form-item {
    &:first-child {
      margin-right: 16px;
    }
    ::v-deep {
      .el-input,
      .el-input-number {
        width: 210px;
      }
    }
  }
}
</style>

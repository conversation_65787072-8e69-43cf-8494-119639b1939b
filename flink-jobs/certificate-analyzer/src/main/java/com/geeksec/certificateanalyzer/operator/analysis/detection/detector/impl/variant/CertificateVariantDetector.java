package com.geeksec.certificateanalyzer.operator.analysis.detection.detector.impl.variant;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.detection.detector.BaseCertificateDetector;
import com.geeksec.certificateanalyzer.repository.CertificateRepository;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * 证书关联检测器
 * <p>
 * 检测证书是否在知识库中存在关联证书，对应 NTA 2.0 中的 checkCollisionCert 逻辑：
 * - 通过证书的 PEM MD5 值查询知识库
 * - 检查是否存在相同 MD5 但不同 SHA1 的证书记录
 * - 如果存在，则标记为关联证书（证书变体或不同编码格式）
 * 
 * 业务含义：
 * 这不是检测 MD5 碰撞攻击，而是检测同一证书的不同表示形式：
 * - 同一证书的不同编码格式（DER/PEM）
 * - 证书的不同版本或变体
 * - 与知识库中已知证书的关联关系
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class CertificateVariantDetector extends BaseCertificateDetector {

    /** 证书数据仓库 */
    private final CertificateRepository certificateRepository;

    public CertificateVariantDetector(CertificateRepository certificateRepository) {
        super("Certificate Variant Detector");
        this.certificateRepository = certificateRepository;
    }

    @Override
    protected void doDetect(X509Certificate cert) {
        if (cert == null) {
            return;
        }

        try {
            // 检查证书变体
            if (hasCertificateVariants(cert)) {
                cert.getLabels().add(CertificateLabel.SPECIAL_EXTENSION);
                log.debug("检测到证书变体: PemMD5={}, ASN1SHA1={}",
                         cert.getPemMd5(), cert.getDerSha1());
            }

        } catch (Exception e) {
            log.debug("证书变体检测过程中发生异常: {}", e.getMessage());
        }
    }

    /**
     * 检查证书是否存在变体
     * <p>
     * 检测逻辑：
     * 1. 获取证书的 PEM MD5 和 ASN1 SHA1
     * 2. 查询数据库中是否存在相同 MD5 但不同 SHA1 的证书
     * 3. 如果存在，则判定为证书变体
     *
     * @param cert 证书对象
     * @return 如果存在变体返回 true，否则返回 false
     */
    private boolean hasCertificateVariants(X509Certificate cert) {
        String pemMd5 = cert.getPemMd5();
        String asn1Sha1 = cert.getDerSha1();

        if (pemMd5 == null || pemMd5.isEmpty() || 
            asn1Sha1 == null || asn1Sha1.isEmpty()) {
            return false;
        }

        try {
            // 使用证书仓库检查变体
            boolean hasVariants = certificateRepository.checkCollisionCert(pemMd5, asn1Sha1);
            
            if (hasVariants) {
                log.debug("发现证书变体: 相同MD5({})但不同SHA1({})", pemMd5, asn1Sha1);
            }
            
            return hasVariants;

        } catch (Exception e) {
            log.error("检查证书变体失败: pemMd5={}, asn1Sha1={}", pemMd5, asn1Sha1, e);
            return false;
        }
    }

    /**
     * 检查特定的已知变体证书
     * <p>
     * 某些已知的特殊证书可以直接通过 SHA1 值识别
     * 例如：5169d4ff45d5443dc236516f9e26ee21164bb442 是一个已知的特殊证书
     *
     * @param cert 证书对象
     * @return 如果是已知特殊证书返回 true，否则返回 false
     */
    private boolean isKnownSpecialCertificate(X509Certificate cert) {
        String asn1Sha1 = cert.getDerSha1();
        
        if (asn1Sha1 == null) {
            return false;
        }

        // 已知的特殊证书 SHA1 列表
        // 这个列表可以从知识库服务获取，这里先硬编码一个示例
        return "5169d4ff45d5443dc236516f9e26ee21164bb442".equals(asn1Sha1);
    }

    /**
     * 获取证书仓库（用于测试）
     *
     * @return 证书仓库实例
     */
    public CertificateRepository getCertificateRepository() {
        return certificateRepository;
    }
}

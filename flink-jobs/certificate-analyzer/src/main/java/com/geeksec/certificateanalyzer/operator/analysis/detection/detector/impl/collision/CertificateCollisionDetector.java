package com.geeksec.certificateanalyzer.operator.analysis.detection.detector.impl.collision;

import com.geeksec.certificateanalyzer.enums.CertificateLabel;
import com.geeksec.certificateanalyzer.model.cert.X509Certificate;
import com.geeksec.certificateanalyzer.operator.analysis.detection.detector.BaseCertificateDetector;
import com.geeksec.certificateanalyzer.repository.CertificateRepository;
import lombok.extern.slf4j.Slf4j;

/**
 * 证书碰撞检测器
 * <p>
 * 检测证书是否存在 MD5 碰撞：
 * - 通过证书的 PEM MD5 值查询知识库
 * - 检查是否存在相同 MD5 但不同 SHA1 的证书记录
 * - 如果存在，则说明发生了 MD5 碰撞（两个不同证书产生相同 MD5）
 *
 * MD5 碰撞的安全意义：
 * - 表明存在两个内容不同但 MD5 相同的证书
 * - 可能被用于绕过基于 MD5 的安全检查
 * - 需要特别关注此类证书的安全风险
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class CertificateCollisionDetector extends BaseCertificateDetector {

    /** 证书数据仓库 */
    private final CertificateRepository certificateRepository;

    public CertificateCollisionDetector(CertificateRepository certificateRepository) {
        super("Certificate Collision Detector");
        this.certificateRepository = certificateRepository;
    }

    @Override
    protected void doDetect(X509Certificate cert) {
        if (cert == null) {
            return;
        }

        try {
            // 检查证书碰撞
            if (isCollisionCertificate(cert)) {
                cert.getLabels().add(CertificateLabel.MD5_COLLISION);
                log.debug("检测到 MD5 哈希碰撞证书: PemMD5={}, ASN1SHA1={}",
                         cert.getPemMd5(), cert.getDerSha1());
            }

        } catch (Exception e) {
            log.debug("证书碰撞检测过程中发生异常: {}", e.getMessage());
        }
    }

    /**
     * 检查证书是否存在 MD5 哈希碰撞
     * <p>
     * MD5 哈希碰撞检测逻辑：
     * 1. 获取证书的 PEM MD5 和 ASN1 SHA1
     * 2. 查询数据库中是否存在相同 MD5 但不同 SHA1 的证书
     * 3. 如果存在，则判定为 MD5 哈希碰撞（两个不同证书产生相同 MD5）
     *
     * @param cert 证书对象
     * @return 如果存在 MD5 哈希碰撞返回 true，否则返回 false
     */
    private boolean isCollisionCertificate(X509Certificate cert) {
        String pemMd5 = cert.getPemMd5();
        String asn1Sha1 = cert.getDerSha1();

        if (pemMd5 == null || pemMd5.isEmpty() || 
            asn1Sha1 == null || asn1Sha1.isEmpty()) {
            return false;
        }

        try {
            // 使用证书仓库检查碰撞
            boolean isCollision = certificateRepository.checkCollisionCert(pemMd5, asn1Sha1);
            
            if (isCollision) {
                log.debug("发现 MD5 哈希碰撞: 相同MD5({})但不同SHA1({})", pemMd5, asn1Sha1);
            }
            
            return isCollision;

        } catch (Exception e) {
            log.error("检查证书 MD5 哈希碰撞失败: pemMd5={}, asn1Sha1={}", pemMd5, asn1Sha1, e);
            return false;
        }
    }

    /**
     * 检查特定的已知碰撞证书
     * <p>
     * 某些已知的碰撞证书可以直接通过 SHA1 值识别
     * 例如：5169d4ff45d5443dc236516f9e26ee21164bb442 是一个已知的碰撞证书
     *
     * @param cert 证书对象
     * @return 如果是已知碰撞证书返回 true，否则返回 false
     */
    private boolean isKnownCollisionCertificate(X509Certificate cert) {
        String asn1Sha1 = cert.getDerSha1();
        
        if (asn1Sha1 == null) {
            return false;
        }

        // 已知的碰撞证书 SHA1 列表
        // 这个列表可以从知识库服务获取，这里先硬编码一个示例
        return "5169d4ff45d5443dc236516f9e26ee21164bb442".equals(asn1Sha1);
    }

    /**
     * 获取证书仓库（用于测试）
     *
     * @return 证书仓库实例
     */
    public CertificateRepository getCertificateRepository() {
        return certificateRepository;
    }
}
